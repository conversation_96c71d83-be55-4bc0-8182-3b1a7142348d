#!/usr/bin/env python3
"""
基于FEniCSx的三层柔性圆板有限元分析

结构层次（从下到上）:
1. 柔性PCB载板（聚酰亚胺PI基材）- 25μm
2. PVDF压电薄膜 - 35μm  
3. 电极层（导电银胶/金属） - 5μm

分析目标：
- 在中心施加载荷，分析整体位移和应力分布
- 对各层进行强度校核
- 生成网格图和应力分布图
"""

import numpy as np
import matplotlib.pyplot as plt
from mpi4py import MPI
import dolfinx
from dolfinx import mesh, fem, io, default_scalar_type
from dolfinx.fem.petsc import LinearProblem
import ufl
from petsc4py import PETSc
import gmsh
import warnings
warnings.filterwarnings('ignore')

class FlexibleCircularPlateFEniCSx:

    """三层柔性圆形压力传感器有限元分析类"""
    
    def __init__(self, radius=0.025, force_magnitude=10.0, mesh_resolution=30):
        """
        初始化分析参数
        
        Args:
            radius: 圆板半径 (m)
            force_magnitude: 中心施加载荷 (N)
            mesh_resolution: 网格密度
        """
        self.radius = radius
        self.force_magnitude = force_magnitude
        self.mesh_resolution = mesh_resolution
        
        # MPI通信
        self.comm = MPI.COMM_WORLD
        
        # 各层厚度 (m)
        self.thickness_pcb = 25e-6      # PI基材: 25μm
        self.thickness_pvdf = 35e-6     # PVDF薄膜: 35μm
        self.thickness_electrode = 5e-6  # 电极层: 5μm
        self.total_thickness = self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode
        
        # 材料属性
        self.define_material_properties()
        
        # 网格和函数空间
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        
        print(f"=== FEniCSx三层柔性圆板分析 ===")
        print(f"圆板半径: {self.radius*1000:.1f} mm")
        print(f"总厚度: {self.total_thickness*1e6:.1f} μm")
        print(f"中心载荷: {self.force_magnitude} N")
        
    def define_material_properties(self):
        """定义各层材料属性"""
        
        # PI基材层属性
        self.pcb_props = {
            'E': 2.8e9,           # 弹性模量 (Pa)
            'nu': 0.35,           # 泊松比
            'density': 1420,      # 密度 (kg/m³)
            'yield_strength': 85e6,  # 屈服强度 (Pa)
            'name': 'PI基材'
        }
        
        # PVDF薄膜属性
        self.pvdf_props = {
            'E': 2.0e9,           # 弹性模量 (Pa)
            'nu': 0.35,           # 泊松比
            'density': 1780,      # 密度 (kg/m³)
            'yield_strength': 50e6,  # 屈服强度 (Pa)
            'name': 'PVDF薄膜'
        }
        
        # 电极层属性
        self.electrode_props = {
            'E': 10e9,            # 弹性模量 (Pa)
            'nu': 0.37,           # 泊松比
            'density': 4000,      # 密度 (kg/m³)
            'yield_strength': 30e6,  # 屈服强度 (Pa)
            'name': '电极层'
        }
        
        # 计算等效材料属性（厚度加权平均）
        w_pcb = self.thickness_pcb / self.total_thickness
        w_pvdf = self.thickness_pvdf / self.total_thickness
        w_electrode = self.thickness_electrode / self.total_thickness
        
        self.E_eff = (w_pcb * self.pcb_props['E'] + 
                     w_pvdf * self.pvdf_props['E'] + 
                     w_electrode * self.electrode_props['E'])
        
        self.nu_eff = (w_pcb * self.pcb_props['nu'] + 
                      w_pvdf * self.pvdf_props['nu'] + 
                      w_electrode * self.electrode_props['nu'])
        
        # 平面应力条件下的材料常数
        self.lambda_eff = self.E_eff * self.nu_eff / ((1 + self.nu_eff) * (1 - self.nu_eff))
        self.mu_eff = self.E_eff / (2 * (1 + self.nu_eff))
        
        print(f"等效材料属性:")
        print(f"  弹性模量: {self.E_eff/1e9:.2f} GPa")
        print(f"  泊松比: {self.nu_eff:.3f}")
        print(f"  Lamé参数 λ: {self.lambda_eff/1e9:.2f} GPa")
        print(f"  剪切模量 μ: {self.mu_eff/1e9:.2f} GPa")
        
    def create_mesh(self):
        """使用Gmsh创建圆形网格"""
        try:
            # 初始化Gmsh
            gmsh.initialize()
            gmsh.clear()

            # 创建圆形几何
            gmsh.model.add("circular_plate")

            # 设置网格尺寸
            mesh_size = self.radius / self.mesh_resolution

            # 创建圆心点
            center = gmsh.model.geo.addPoint(0, 0, 0, mesh_size)

            # 创建圆周上的点
            p1 = gmsh.model.geo.addPoint(self.radius, 0, 0, mesh_size)
            p2 = gmsh.model.geo.addPoint(0, self.radius, 0, mesh_size)
            p3 = gmsh.model.geo.addPoint(-self.radius, 0, 0, mesh_size)
            p4 = gmsh.model.geo.addPoint(0, -self.radius, 0, mesh_size)

            # 创建圆弧
            arc1 = gmsh.model.geo.addCircleArc(p1, center, p2)
            arc2 = gmsh.model.geo.addCircleArc(p2, center, p3)
            arc3 = gmsh.model.geo.addCircleArc(p3, center, p4)
            arc4 = gmsh.model.geo.addCircleArc(p4, center, p1)

            # 创建圆形边界
            circle_loop = gmsh.model.geo.addCurveLoop([arc1, arc2, arc3, arc4])

            # 创建圆形表面
            circle_surface = gmsh.model.geo.addPlaneSurface([circle_loop])

            # 同步几何
            gmsh.model.geo.synchronize()
            
            # 添加物理组
            gmsh.model.addPhysicalGroup(2, [circle_surface], 1)
            gmsh.model.setPhysicalName(2, 1, "domain")

            # 生成网格
            gmsh.model.mesh.generate(2)

            # 转换为DOLFINx网格
            from dolfinx.io import gmshio
            self.domain, cell_markers, facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=2
            )

            # 清理Gmsh
            gmsh.finalize()

            # 创建向量函数空间（2D位移场）
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (2,)))

            print(f"网格创建完成:")
            print(f"  节点数: {self.domain.geometry.x.shape[0]}")
            print(f"  单元数: {self.domain.topology.index_map(2).size_local}")

        except Exception as e:
            print(f"网格创建失败: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
        
    def define_boundary_conditions(self):
        """定义边界条件"""
        # 定义边界：外圆周固定
        def boundary(x):
            r = np.sqrt(x[0]**2 + x[1]**2)
            return np.isclose(r, self.radius, rtol=1e-3)
        
        # 找到边界面
        boundary_facets = mesh.locate_entities_boundary(self.domain, 1, boundary)
        
        # 找到边界自由度
        boundary_dofs = fem.locate_dofs_topological(self.V, 1, boundary_facets)
        
        # 施加零位移边界条件（固定边界）
        u_bc = fem.Function(self.V)
        u_bc.x.array[:] = 0.0
        
        self.bc = fem.dirichletbc(u_bc, boundary_dofs)
        
        print(f"边界条件:")
        print(f"  固定边界面数: {len(boundary_facets)}")
        print(f"  固定自由度数: {len(boundary_dofs)}")
        
    def define_variational_problem(self):
        """定义变分问题"""
        # 定义试验函数和测试函数
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        # 应力张量函数
        def sigma(u):
            return self.lambda_eff * ufl.nabla_div(u) * ufl.Identity(2) + 2 * self.mu_eff * ufl.sym(ufl.grad(u))
        
        # 双线性形式（左端项）
        a = ufl.inner(sigma(u), ufl.grad(v)) * ufl.dx
        
        # 定义载荷：在中心区域施加集中力
        # 使用高斯函数近似点载荷
        x = ufl.SpatialCoordinate(self.domain)
        sigma_load = self.radius / 20  # 载荷分布半径
        load_intensity = self.force_magnitude / (np.pi * sigma_load**2)
        
        # 高斯分布载荷（z方向）
        f_expr = load_intensity * ufl.exp(-(x[0]**2 + x[1]**2) / sigma_load**2)
        f = ufl.as_vector([0, -f_expr])  # 向下的载荷
        
        # 线性形式（右端项）
        L = ufl.inner(f, v) * ufl.dx
        
        return a, L
        
    def solve_problem(self):
        """求解有限元问题"""
        print("求解有限元方程...")
        
        # 定义变分问题
        a, L = self.define_variational_problem()
        
        # 创建线性问题
        problem = LinearProblem(
            a, L, bcs=[self.bc], 
            petsc_options={"ksp_type": "preonly", "pc_type": "lu"}
        )
        
        # 求解
        self.u = problem.solve()
        
        print("求解完成!")
        
        # 计算位移统计信息
        u_array = self.u.x.array.reshape(-1, 2)
        max_displacement = np.max(np.sqrt(np.sum(u_array**2, axis=1)))
        
        print(f"最大位移: {max_displacement*1e6:.3f} μm")
        
    def compute_stress(self):
        """计算应力"""
        print("计算应力场...")
        
        # 创建标量函数空间用于应力
        V_scalar = fem.functionspace(self.domain, ("Lagrange", 1))
        
        # 应力张量
        def sigma(u):
            return self.lambda_eff * ufl.nabla_div(u) * ufl.Identity(2) + 2 * self.mu_eff * ufl.sym(ufl.grad(u))
        
        # von Mises应力
        s = sigma(self.u)
        von_mises = ufl.sqrt(s[0,0]**2 - s[0,0]*s[1,1] + s[1,1]**2 + 3*s[0,1]**2)
        
        # 投影到函数空间
        self.stress = fem.Function(V_scalar)
        stress_expr = fem.Expression(von_mises, V_scalar.element.interpolation_points())
        self.stress.interpolate(stress_expr)
        
        max_stress = np.max(self.stress.x.array)
        print(f"最大von Mises应力: {max_stress/1e6:.2f} MPa")
        
    def save_results(self):
        """保存结果到文件"""
        # 保存位移场
        with io.XDMFFile(self.domain.comm, "displacement.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        # 保存应力场
        with io.XDMFFile(self.domain.comm, "stress.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("结果已保存到 displacement.xdmf 和 stress.xdmf")
        
    def plot_results(self):
        """绘制结果（仅在串行运行时）"""
        if self.comm.rank == 0:
            try:
                # 提取网格和数据
                x = self.domain.geometry.x
                cells = self.domain.topology.connectivity(2, 0).array.reshape(-1, 3)
                
                # 位移幅度
                u_array = self.u.x.array.reshape(-1, 2)
                displacement_magnitude = np.sqrt(np.sum(u_array**2, axis=1))
                
                # 绘制位移云图
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
                
                # 位移图
                im1 = ax1.tripcolor(x[:, 0], x[:, 1], cells, displacement_magnitude*1e6, 
                                   shading='gouraud', cmap='viridis')
                ax1.set_aspect('equal')
                ax1.set_title('位移幅度 (μm)')
                ax1.set_xlabel('X (m)')
                ax1.set_ylabel('Y (m)')
                plt.colorbar(im1, ax=ax1)
                
                # 应力图
                stress_array = self.stress.x.array
                im2 = ax2.tripcolor(x[:, 0], x[:, 1], cells, stress_array/1e6, 
                                   shading='gouraud', cmap='plasma')
                ax2.set_aspect('equal')
                ax2.set_title('von Mises应力 (MPa)')
                ax2.set_xlabel('X (m)')
                ax2.set_ylabel('Y (m)')
                plt.colorbar(im2, ax=ax2)
                
                plt.tight_layout()
                plt.savefig('analysis_results.png', dpi=300, bbox_inches='tight')
                plt.show()
                
                print("结果图已保存为 analysis_results.png")
                
            except Exception as e:
                print(f"绘图时出错: {e}")
                print("结果已保存到XDMF文件，请使用ParaView查看")
                
    def strength_analysis(self):
        """强度校核分析"""
        print("\n=== 强度校核分析 ===")
        
        max_stress = np.max(self.stress.x.array)
        safety_factor_target = 2.0
        
        # 各层材料校核
        layers = [
            ('PI基材', self.pcb_props),
            ('PVDF薄膜', self.pvdf_props),
            ('电极层', self.electrode_props)
        ]
        
        min_safety_factor = float('inf')
        
        for layer_name, props in layers:
            yield_strength = props['yield_strength']
            safety_factor = yield_strength / max_stress
            min_safety_factor = min(min_safety_factor, safety_factor)
            
            print(f"\n{layer_name}:")
            print(f"  屈服强度: {yield_strength/1e6:.1f} MPa")
            print(f"  最大应力: {max_stress/1e6:.2f} MPa")
            print(f"  安全系数: {safety_factor:.2f}")
            
            if safety_factor >= safety_factor_target:
                print(f"  ✓ 安全 (目标: {safety_factor_target})")
            else:
                print(f"  ✗ 不安全 (目标: {safety_factor_target})")
        
        print(f"\n整体评估:")
        print(f"  最小安全系数: {min_safety_factor:.2f}")
        if min_safety_factor >= safety_factor_target:
            print(f"  ✓ 整体结构安全")
        else:
            print(f"  ✗ 需要优化设计")
            
    def run_analysis(self):
        """运行完整分析流程"""
        try:
            # 1. 创建网格
            self.create_mesh()
            
            # 2. 定义边界条件
            self.define_boundary_conditions()
            
            # 3. 求解问题
            self.solve_problem()
            
            # 4. 计算应力
            self.compute_stress()
            
            # 5. 强度校核
            self.strength_analysis()
            
            # 6. 保存和绘制结果
            self.save_results()
            self.plot_results()
            
            print("\n分析完成!")
            
        except Exception as e:
            print(f"分析过程中出错: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    # 创建分析实例
    analyzer = FlexibleCircularPlateFEniCSx(
        radius=0.025,         # 25mm半径
        force_magnitude=10.0, # 10N载荷
        mesh_resolution=40    # 网格密度
    )
    
    # 运行分析
    analyzer.run_analysis()

if __name__ == "__main__":
    main()