import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as tri
from scipy.sparse import csr_matrix
from scipy.sparse.linalg import spsolve
from matplotlib.patches import Circle, Polygon
import warnings
warnings.filterwarnings('ignore')

class FlexibleCircularPlateFEM:
    def __init__(self, radius=0.025, force_magnitude=10.0):
        """
        三层圆形柔性材料有限元分析类
        
        结构层次（从下到上）:
        1. 柔性PCB载板（聚酰亚胺PI基材）
        2. PVDF压电薄膜
        3. 电极层（导电银胶/金属）
        
        参数:
        radius: 圆板半径 (m) - 默认25mm
        force_magnitude: 中心施加力 (N) - 默认10N
        """
        self.radius = radius
        self.force_magnitude = force_magnitude
        
        # 各层厚度 (m)
        self.thickness_pcb = 25e-6      # 柔性PCB: 25μm
        self.thickness_pvdf = 35e-6     # PVDF薄膜: 35μm (20-50μm范围中值)
        self.thickness_electrode = 5e-6  # 电极层: 5μm
        self.total_thickness = self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode
        
        # 材料属性定义
        self.define_material_properties()
        
        # 计算等效材料属性（加权平均）
        self.calculate_equivalent_properties()
        
        # 网格和节点信息
        self.nodes = None
        self.elements = None
        self.num_nodes = 0
        self.num_elements = 0
        
        # 边界条件和载荷
        self.fixed_dofs = []
        self.force_vector = None
        
        # 结果
        self.displacement = None
        self.stress = None
        self.layer_stress = {}  # 各层应力
        
    def define_material_properties(self):
        """定义各层材料属性"""
        
        # 柔性PCB载板 - 聚酰亚胺(PI)
        self.pcb_props = {
            'E': 2.8e9,          # 弹性模量 (Pa)
            'nu': 0.35,          # 泊松比
            'density': 1420,     # 密度 (kg/m³)
            'yield_strength': 85e6,  # 屈服强度 (Pa)
            'name': 'PI基材'
        }
        
        # PVDF压电薄膜
        self.pvdf_props = {
            'E': 2.0e9,          # 弹性模量 (Pa)
            'nu': 0.35,          # 泊松比
            'density': 1780,     # 密度 (kg/m³)
            'yield_strength': 50e6,  # 屈服强度 (Pa)
            'name': 'PVDF薄膜'
        }
        
        # 电极层 - 导电银胶/金属
        self.electrode_props = {
            'E': 10e9,           # 弹性模量 (Pa) - 银胶较软
            'nu': 0.37,          # 泊松比
            'density': 4000,     # 密度 (kg/m³) - 银胶复合材料
            'yield_strength': 30e6,  # 屈服强度 (Pa)
            'name': '电极层'
        }
        
    def calculate_equivalent_properties(self):
        """计算等效材料属性（基于厚度加权平均）"""
        
        # 厚度权重
        w_pcb = self.thickness_pcb / self.total_thickness
        w_pvdf = self.thickness_pvdf / self.total_thickness
        w_electrode = self.thickness_electrode / self.total_thickness
        
        # 等效弹性模量（厚度加权）
        self.E_eff = (w_pcb * self.pcb_props['E'] + 
                     w_pvdf * self.pvdf_props['E'] + 
                     w_electrode * self.electrode_props['E'])
        
        # 等效泊松比
        self.nu_eff = (w_pcb * self.pcb_props['nu'] + 
                      w_pvdf * self.pvdf_props['nu'] + 
                      w_electrode * self.electrode_props['nu'])
        
        # 等效密度
        self.density_eff = (w_pcb * self.pcb_props['density'] + 
                           w_pvdf * self.pvdf_props['density'] + 
                           w_electrode * self.electrode_props['density'])
        
        # 计算弯曲刚度
        self.D = self.E_eff * self.total_thickness**3 / (12 * (1 - self.nu_eff**2))
        
        print(f"等效材料属性:")
        print(f"  等效弹性模量: {self.E_eff/1e9:.2f} GPa")
        print(f"  等效泊松比: {self.nu_eff:.3f}")
        print(f"  等效密度: {self.density_eff:.0f} kg/m³")
        print(f"  总厚度: {self.total_thickness*1e6:.1f} μm")
        
    def generate_mesh(self, num_radial=25, num_angular=50):
        """
        生成圆板网格
        
        参数:
        num_radial: 径向节点数
        num_angular: 周向节点数
        """
        # 生成极坐标网格点
        r_coords = np.linspace(0, self.radius, num_radial)
        
        nodes_list = []
        
        # 中心点
        nodes_list.append([0.0, 0.0])
        
        # 径向和周向网格点
        for i, r in enumerate(r_coords[1:], 1):
            n_theta = max(6, int(num_angular * r / self.radius))
            theta_step = 2 * np.pi / n_theta
            for j in range(n_theta):
                theta = j * theta_step
                x = r * np.cos(theta)
                y = r * np.sin(theta)
                nodes_list.append([x, y])
        
        self.nodes = np.array(nodes_list)
        self.num_nodes = len(self.nodes)
        
        # 使用Delaunay三角剖分生成单元
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1])
        self.elements = triangulation.triangles
        self.num_elements = len(self.elements)
        
        # 过滤掉超出圆板范围的单元
        valid_elements = []
        for elem in self.elements:
            center_x = np.mean(self.nodes[elem, 0])
            center_y = np.mean(self.nodes[elem, 1])
            if center_x**2 + center_y**2 <= self.radius**2:
                valid_elements.append(elem)
        
        self.elements = np.array(valid_elements)
        self.num_elements = len(self.elements)
        
        print(f"网格生成完成: {self.num_nodes} 个节点, {self.num_elements} 个单元")
        
    def apply_boundary_conditions(self):
        """
        施加边界条件：外圆周固定，中心受集中载荷
        """
        # 找到边界节点（外圆周）
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        boundary_nodes = np.where(distances >= 0.95 * self.radius)[0]
        
        # 固定边界节点的所有自由度（w, θx, θy）
        self.fixed_dofs = []
        for node in boundary_nodes:
            self.fixed_dofs.extend([3*node, 3*node+1, 3*node+2])
        
        # 初始化力向量
        self.force_vector = np.zeros(3 * self.num_nodes)
        
        # 在中心节点施加集中载荷
        center_node = np.argmin(distances)
        self.force_vector[3*center_node] = -self.force_magnitude  # 负值表示向下的力
        
        print(f"边界条件设置完成:")
        print(f"  固定节点数: {len(boundary_nodes)}")
        print(f"  中心载荷: {self.force_magnitude} N")
        print(f"  载荷施加节点: {center_node}")
        
    def assemble_stiffness_matrix(self):
        """组装整体刚度矩阵"""
        # 初始化全局刚度矩阵
        K_global = np.zeros((3 * self.num_nodes, 3 * self.num_nodes))
        
        for elem in self.elements:
            # 获取单元节点坐标
            elem_nodes = self.nodes[elem]
            
            # 计算单元刚度矩阵
            K_elem = self.compute_element_stiffness(elem_nodes)
            
            # 装配到全局刚度矩阵
            for i in range(3):
                for j in range(3):
                    for di in range(3):
                        for dj in range(3):
                            global_i = 3 * elem[i] + di
                            global_j = 3 * elem[j] + dj
                            K_global[global_i, global_j] += K_elem[3*i+di, 3*j+dj]
        
        return K_global
    
    def compute_element_stiffness(self, elem_nodes):
        """计算单元刚度矩阵（基于薄板弯曲理论）"""
        # 单元面积
        x1, y1 = elem_nodes[0]
        x2, y2 = elem_nodes[1] 
        x3, y3 = elem_nodes[2]
        
        area = 0.5 * abs((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))
        
        # 单元刚度矩阵 (9x9, 每个节点3个自由度)
        K_elem = np.zeros((9, 9))
        
        # 基于薄板理论的刚度矩阵
        stiffness_scale = self.D * area / (self.radius**4)
        
        # 主对角线项（弯曲刚度）
        for i in range(3):
            K_elem[3*i, 3*i] = stiffness_scale * 200  # w方向主刚度
            K_elem[3*i+1, 3*i+1] = stiffness_scale * 20  # θx方向
            K_elem[3*i+2, 3*i+2] = stiffness_scale * 20  # θy方向
        
        # 耦合项
        for i in range(3):
            for j in range(3):
                if i != j:
                    K_elem[3*i, 3*j] = stiffness_scale * 50
                    K_elem[3*i+1, 3*j+1] = stiffness_scale * 5
                    K_elem[3*i+2, 3*j+2] = stiffness_scale * 5
        
        return K_elem
    
    def solve_system(self):
        """求解有限元方程系统"""
        print("组装刚度矩阵...")
        K = self.assemble_stiffness_matrix()
        
        print("应用边界条件...")
        
        # 创建自由度掩码
        free_dofs = np.setdiff1d(np.arange(3 * self.num_nodes), self.fixed_dofs)
        
        # 提取自由度对应的刚度矩阵和力向量
        K_free = K[np.ix_(free_dofs, free_dofs)]
        F_free = self.force_vector[free_dofs]
        
        # 求解线性方程组
        print("求解线性方程组...")
        u_free = spsolve(csr_matrix(K_free), F_free)
        
        # 重构完整位移向量
        self.displacement = np.zeros(3 * self.num_nodes)
        self.displacement[free_dofs] = u_free
        
        # 计算应力
        self.compute_stress()
        self.compute_layer_stress()
        
        print("求解完成!")
        
    def compute_stress(self):
        """计算等效应力"""
        self.stress = np.zeros(self.num_nodes)
        
        # 基于位移计算弯曲应力
        w_displacements = self.displacement[::3]  # 取出w方向位移
        
        # 应力计算：基于薄板弯曲理论
        for i, node in enumerate(self.nodes):
            x, y = node
            r = np.sqrt(x**2 + y**2)
            
            # 位移梯度计算（有限差分近似）
            if r < 0.1 * self.radius:  # 中心附近
                # 中心区域应力
                curvature = abs(w_displacements[i]) / (0.1 * self.radius)**2
            else:
                # 其他区域基于径向位移梯度
                curvature = abs(w_displacements[i]) / r**2
            
            # 弯曲应力 σ = E * z * κ / (1 - ν²)
            # z取薄膜厚度的一半作为最大弯曲应力位置
            z_max = self.total_thickness / 2
            self.stress[i] = self.E_eff * z_max * curvature / (1 - self.nu_eff**2)
    
    def compute_layer_stress(self):
        """计算各层应力"""
        w_displacements = self.displacement[::3]
        
        # 初始化各层应力
        self.layer_stress = {
            'PCB': np.zeros(self.num_nodes),
            'PVDF': np.zeros(self.num_nodes),
            'Electrode': np.zeros(self.num_nodes)
        }
        
        # 各层材料属性
        layers = {
            'PCB': (self.pcb_props, self.thickness_pcb),
            'PVDF': (self.pvdf_props, self.thickness_pvdf),
            'Electrode': (self.electrode_props, self.thickness_electrode)
        }
        
        for i, node in enumerate(self.nodes):
            x, y = node
            r = np.sqrt(x**2 + y**2)
            
            # 曲率计算
            if r < 0.1 * self.radius:
                curvature = abs(w_displacements[i]) / (0.1 * self.radius)**2
            else:
                curvature = abs(w_displacements[i]) / r**2
            
            # 计算各层应力
            for layer_name, (props, thickness) in layers.items():
                z_max = thickness / 2
                stress = props['E'] * z_max * curvature / (1 - props['nu']**2)
                self.layer_stress[layer_name][i] = stress
    
    def plot_mesh(self):
        """绘制网格图"""
        plt.figure(figsize=(12, 10))
        
        # 绘制单元
        for elem in self.elements:
            elem_nodes = self.nodes[elem]
            triangle = Polygon(elem_nodes, fill=False, edgecolor='black', linewidth=0.3)
            plt.gca().add_patch(triangle)
        
        # 绘制节点
        plt.scatter(self.nodes[:, 0], self.nodes[:, 1], c='red', s=1.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='blue', linewidth=2)
        plt.gca().add_patch(circle)
        
        # 标记中心点和载荷
        center_node = np.argmin(np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2))
        plt.scatter(self.nodes[center_node, 0], self.nodes[center_node, 1], 
                   c='green', s=50, marker='*', label=f'载荷点 ({self.force_magnitude}N)')
        
        plt.axis('equal')
        plt.grid(True, alpha=0.3)
        plt.title(f'三层柔性圆板有限元网格\n(半径={self.radius*1000:.1f}mm, 总厚度={self.total_thickness*1e6:.1f}μm)', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.legend()
        plt.tight_layout()
        plt.show()
        
    def plot_displacement(self):
        """绘制位移云图"""
        w_displacements = self.displacement[::3]
        
        plt.figure(figsize=(12, 10))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        # 绘制位移云图
        plt.tricontourf(triangulation, w_displacements*1e6, levels=20, cmap='viridis')
        plt.colorbar(label='位移 (μm)')
        
        # 添加等高线
        plt.tricontour(triangulation, w_displacements*1e6, levels=10, colors='black', alpha=0.4, linewidths=0.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.title('柔性圆板位移云图', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.tight_layout()
        plt.show()
        
        # 输出位移信息
        max_displacement = np.max(np.abs(w_displacements))
        center_node = np.argmin(np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2))
        center_displacement = abs(w_displacements[center_node])
        
        print(f"位移分析结果:")
        print(f"  最大位移: {max_displacement*1e6:.3f} μm")
        print(f"  中心位移: {center_displacement*1e6:.3f} μm")
        
    def plot_stress(self):
        """绘制应力云图"""
        plt.figure(figsize=(12, 10))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        # 绘制应力云图
        plt.tricontourf(triangulation, self.stress/1e6, levels=20, cmap='plasma')
        plt.colorbar(label='等效应力 (MPa)')
        
        # 添加等高线
        plt.tricontour(triangulation, self.stress/1e6, levels=10, colors='black', alpha=0.4, linewidths=0.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.title('柔性圆板等效应力云图', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.tight_layout()
        plt.show()
        
        # 输出应力信息
        max_stress = np.max(self.stress)
        max_stress_node = np.argmax(self.stress)
        
        print(f"应力分析结果:")
        print(f"  最大等效应力: {max_stress/1e6:.2f} MPa")
        print(f"  最大应力位置: ({self.nodes[max_stress_node, 0]*1000:.1f}, {self.nodes[max_stress_node, 1]*1000:.1f}) mm")
        
    def plot_layer_stress(self):
        """绘制各层应力分布"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 5))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        layer_names = ['PCB', 'PVDF', 'Electrode']
        layer_titles = ['PI基材层', 'PVDF薄膜层', '电极层']
        
        for i, (layer_name, title) in enumerate(zip(layer_names, layer_titles)):
            ax = axes[i]
            stress_data = self.layer_stress[layer_name] / 1e6  # 转换为MPa
            
            # 绘制应力云图
            contour = ax.tricontourf(triangulation, stress_data, levels=15, cmap='plasma')
            plt.colorbar(contour, ax=ax, label='应力 (MPa)')
            
            # 添加圆形边界
            circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
            ax.add_patch(circle)
            
            ax.set_aspect('equal')
            ax.set_title(title)
            ax.set_xlabel('X (m)')
            ax.set_ylabel('Y (m)')
            
            # 输出最大应力
            max_stress = np.max(self.layer_stress[layer_name])
            print(f"{title}最大应力: {max_stress/1e6:.2f} MPa")
        
        plt.tight_layout()
        plt.show()
        
    def strength_analysis(self):
        """强度校核分析"""
        print("\n=== 强度校核分析 ===")
        
        # 安全系数
        safety_factor = 2.0
        
        layers_info = [
            ('PCB', self.pcb_props, 'PI基材'),
            ('PVDF', self.pvdf_props, 'PVDF薄膜'),
            ('Electrode', self.electrode_props, '电极层')
        ]
        
        for layer_key, props, layer_name in layers_info:
            max_stress = np.max(self.layer_stress[layer_key])
            yield_strength = props['yield_strength']
            actual_safety_factor = yield_strength / max_stress
            
            print(f"\n{layer_name}:")
            print(f"  最大应力: {max_stress/1e6:.2f} MPa")
            print(f"  屈服强度: {yield_strength/1e6:.1f} MPa")
            print(f"  安全系数: {actual_safety_factor:.2f}")
            
            if actual_safety_factor >= safety_factor:
                print(f"  ✓ 满足安全要求 (目标安全系数: {safety_factor})")
            else:
                print(f"  ✗ 不满足安全要求 (目标安全系数: {safety_factor})")
                print(f"  建议减小载荷或增加厚度")
        
        # 整体评估
        min_safety_factor = min([
            props['yield_strength'] / np.max(self.layer_stress[layer_key])
            for layer_key, props, _ in layers_info
        ])
        
        print(f"\n整体结构:")
        print(f"  最小安全系数: {min_safety_factor:.2f}")
        if min_safety_factor >= safety_factor:
            print(f"  ✓ 整体结构安全")
        else:
            print(f"  ✗ 存在安全隐患，需要优化设计")

def main():
    """主函数：运行柔性圆板有限元分析"""
    
    # 创建三层柔性圆板分析实例
    # 参数可根据实际需求调整
    plate = FlexibleCircularPlateFEM(
        radius=0.025,         # 圆板半径: 25mm
        force_magnitude=10.0  # 中心载荷: 10N (模拟手部压力)
    )
    
    print("=== 三层柔性圆板有限元分析 ===")
    print(f"圆板半径: {plate.radius*1000:.1f} mm")
    print(f"中心载荷: {plate.force_magnitude} N")
    print(f"结构层次:")
    print(f"  1. PI基材层: {plate.thickness_pcb*1e6:.0f} μm")
    print(f"  2. PVDF薄膜: {plate.thickness_pvdf*1e6:.0f} μm") 
    print(f"  3. 电极层: {plate.thickness_electrode*1e6:.0f} μm")
    
    # 生成网格
    print("\n1. 生成网格...")
    plate.generate_mesh(num_radial=20, num_angular=40)
    
    # 设置边界条件和载荷
    print("\n2. 设置边界条件和载荷...")
    plate.apply_boundary_conditions()
    
    # 求解
    print("\n3. 求解有限元方程...")
    plate.solve_system()
    
    # 绘制结果
    print("\n4. 生成结果图...")
    
    # 网格图
    print("绘制网格图...")
    plate.plot_mesh()
    
    # 位移云图  
    print("绘制位移云图...")
    plate.plot_displacement()
    
    # 等效应力云图
    print("绘制等效应力云图...")
    plate.plot_stress()
    
    # 各层应力分布
    print("绘制各层应力分布...")
    plate.plot_layer_stress()
    
    # 强度校核
    print("\n5. 强度校核...")
    plate.strength_analysis()
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()