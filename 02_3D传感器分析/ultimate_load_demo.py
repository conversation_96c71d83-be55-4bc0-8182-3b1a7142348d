#!/usr/bin/env python3
"""
极限载荷分析演示
展示柔性传感器的破坏载荷分析
"""
import numpy as np
import matplotlib.pyplot as plt

class UltimateLoadAnalysisDemo:
    def __init__(self):
        """演示柔性传感器的极限载荷分析"""
        # 基于前面的分析结果设置参数
        self.design_load = 20.0  # N (设计载荷)
        
        # 材料属性 (基于前面的分析)
        self.materials = {
            'PCB基材': {
                'yield_strength': 6e6,    # Pa
                'ultimate_strength': 8e6, # Pa
                'E': 4e6,                 # Pa
                'color': '#1f77b4'
            },
            'PVDF薄膜': {
                'yield_strength': 60e6,   # Pa
                'ultimate_strength': 80e6, # Pa
                'E': 3e9,                 # Pa
                'color': '#ff7f0e'
            },
            '电极层': {
                'yield_strength': 45e6,   # Pa
                'ultimate_strength': 60e6, # Pa
                'E': 200e6,               # Pa
                'color': '#2ca02c'
            },
            '接触层': {
                'yield_strength': 25e6,   # Pa
                'ultimate_strength': 30e6, # Pa
                'E': 100e6,               # Pa
                'color': '#d62728'
            }
        }
        
        # 基于FEM分析的应力放大系数 (在20N时测得)
        self.stress_amplification = 386.8
        self.base_pressure = 20.0 / (np.pi * (4e-3)**2)  # 基础压力 Pa
        
    def calculate_stress_at_load(self, load):
        """根据载荷计算各层材料的应力"""
        # 简化的线性模型 (实际中可能是非线性的)
        load_factor = load / self.design_load
        base_stress = self.base_pressure * self.stress_amplification * load_factor
        
        # 不同材料层的应力分布系数 (基于FEM结果)
        layer_factors = {
            'PCB基材': 0.026,    # 相对较低
            'PVDF薄膜': 1.0,     # 最高应力
            '电极层': 0.039,     # 中等
            '接触层': 0.050      # 中等
        }
        
        stresses = {}
        for material, factor in layer_factors.items():
            stresses[material] = base_stress * factor
            
        return stresses
    
    def perform_ultimate_load_analysis(self):
        """执行极限载荷分析"""
        print("=== 柔性传感器极限载荷分析 ===")
        print(f"设计载荷: {self.design_load} N")
        print(f"应力放大系数: {self.stress_amplification:.1f}")
        
        # 载荷范围：从1N到100N
        loads = np.linspace(1, 100, 50)
        
        analysis_results = {
            'loads': loads,
            'first_yield': None,
            'first_failure': None,
            'ultimate_load': None,
            'critical_material': None,
            'safety_factors': [],
            'max_stresses': []
        }
        
        print(f"\n正在分析载荷范围: {loads[0]:.1f} - {loads[-1]:.1f} N...")
        
        for load in loads:
            stresses = self.calculate_stress_at_load(load)
            
            # 计算当前载荷下的安全系数和失效状态
            min_sf_yield = float('inf')
            min_sf_ultimate = float('inf')
            max_stress = 0
            critical_mat = None
            
            has_yielded = False
            has_failed = False
            
            for material, stress in stresses.items():
                props = self.materials[material]
                
                sf_yield = props['yield_strength'] / stress if stress > 0 else float('inf')
                sf_ultimate = props['ultimate_strength'] / stress if stress > 0 else float('inf')
                
                if sf_yield < min_sf_yield:
                    min_sf_yield = sf_yield
                    critical_mat = material
                
                if sf_ultimate < min_sf_ultimate:
                    min_sf_ultimate = sf_ultimate
                
                if stress > max_stress:
                    max_stress = stress
                
                # 检查失效条件
                if stress >= props['yield_strength']:
                    has_yielded = True
                
                if stress >= props['ultimate_strength']:
                    has_failed = True
            
            analysis_results['safety_factors'].append(min_sf_yield)
            analysis_results['max_stresses'].append(max_stress)
            
            # 记录关键事件
            if has_yielded and analysis_results['first_yield'] is None:
                analysis_results['first_yield'] = load
                analysis_results['critical_material'] = critical_mat
                print(f"  ⚠️  首次屈服载荷: {load:.1f} N ({critical_mat})")
            
            if has_failed and analysis_results['first_failure'] is None:
                analysis_results['first_failure'] = load
                print(f"  🔴 首次失效载荷: {load:.1f} N ({critical_mat})")
            
            # 如果多个材料都失效，认为是极限载荷
            failed_materials = sum(1 for mat, stress in stresses.items() 
                                 if stress >= self.materials[mat]['ultimate_strength'])
            if failed_materials >= 2 and analysis_results['ultimate_load'] is None:
                analysis_results['ultimate_load'] = load
                print(f"  💥 极限载荷 (多材料失效): {load:.1f} N")
        
        return analysis_results
    
    def generate_summary_report(self, results):
        """生成分析报告"""
        print(f"\n=== 极限载荷分析报告 ===")
        
        if results['first_yield']:
            yield_sf = results['first_yield'] / self.design_load
            print(f"首次屈服载荷: {results['first_yield']:.1f} N")
            print(f"屈服安全系数: {yield_sf:.2f}")
            print(f"关键材料: {results['critical_material']}")
        
        if results['first_failure']:
            failure_sf = results['first_failure'] / self.design_load
            print(f"首次失效载荷: {results['first_failure']:.1f} N")
            print(f"破坏安全系数: {failure_sf:.2f}")
        
        if results['ultimate_load']:
            ultimate_sf = results['ultimate_load'] / self.design_load
            print(f"极限载荷: {results['ultimate_load']:.1f} N")
            print(f"极限安全系数: {ultimate_sf:.2f}")
        
        # 安全性评估
        if results['first_failure']:
            safety_margin = results['first_failure'] / self.design_load
            if safety_margin >= 3.0:
                status = "✅ 非常安全"
            elif safety_margin >= 2.0:
                status = "✅ 安全"
            elif safety_margin >= 1.5:
                status = "⚠️ 勉强可接受"
            else:
                status = "❌ 不安全"
            
            print(f"\n安全性评估: {status}")
            print(f"可承受的最大载荷: {results['first_failure']:.1f} N")
            print(f"推荐工作载荷: {results['first_failure']/2:.1f} N (50%安全余量)")
        
        # 使用建议
        print(f"\n使用建议:")
        if results['first_failure'] and results['first_failure'] < self.design_load * 1.5:
            print(f"  • 当前设计载荷{self.design_load}N过高，建议降低至{results['first_failure']/2:.1f}N")
            print(f"  • 考虑增加传感器厚度或使用更高强度材料")
            print(f"  • 关注{results['critical_material']}的材料选择")
        
        return results
    
    def plot_load_curves(self, results):
        """绘制载荷-失效曲线"""
        try:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            
            loads = results['loads']
            max_stresses = [s/1e6 for s in results['max_stresses']]  # 转换为MPa
            safety_factors = results['safety_factors']
            
            # 确保数组长度一致
            min_length = min(len(loads), len(max_stresses), len(safety_factors))
            loads = loads[:min_length]
            max_stresses = max_stresses[:min_length]
            safety_factors = safety_factors[:min_length]
            
            # 载荷-应力曲线
            ax1.plot(loads, max_stresses, 'b-', linewidth=2, label='最大应力')
            ax1.set_xlabel('载荷 (N)')
            ax1.set_ylabel('最大应力 (MPa)')
            ax1.set_title('载荷 vs 最大应力')
            ax1.grid(True, alpha=0.3)
            
            # 标记关键点
            if results['first_yield']:
                ax1.axvline(x=results['first_yield'], color='orange', linestyle='--', 
                           label=f'首次屈服 ({results["first_yield"]:.1f}N)')
            if results['first_failure']:
                ax1.axvline(x=results['first_failure'], color='red', linestyle='--', 
                           label=f'首次失效 ({results["first_failure"]:.1f}N)')
            if results['ultimate_load']:
                ax1.axvline(x=results['ultimate_load'], color='darkred', linestyle='-', 
                           label=f'极限载荷 ({results["ultimate_load"]:.1f}N)')
            
            ax1.legend()
            
            # 载荷-安全系数曲线
            ax2.plot(loads, safety_factors, 'r-', linewidth=2, label='安全系数')
            ax2.axhline(y=1.0, color='red', linestyle='-', alpha=0.5, label='失效线 (SF=1)')
            ax2.axhline(y=2.0, color='orange', linestyle='-', alpha=0.5, label='安全线 (SF=2)')
            ax2.axhline(y=3.0, color='green', linestyle='-', alpha=0.5, label='推荐线 (SF=3)')
            
            ax2.set_xlabel('载荷 (N)')
            ax2.set_ylabel('安全系数')
            ax2.set_title('载荷 vs 安全系数')
            ax2.set_ylim(0, min(10, max(safety_factors) * 1.1))
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            
            plt.tight_layout()
            plt.savefig('/root/FEM/ultimate_load_analysis_demo.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"\n载荷-失效分析图已保存: ultimate_load_analysis_demo.png")
        except Exception as e:
            print(f"绘图失败: {e}")
            print("跳过图形生成，继续分析...")

def main():
    """主函数"""
    demo = UltimateLoadAnalysisDemo()
    
    # 执行极限载荷分析
    results = demo.perform_ultimate_load_analysis()
    
    # 生成报告
    demo.generate_summary_report(results)
    
    # 绘制曲线
    demo.plot_load_curves(results)
    
    print(f"\n=== 分析完成 ===")
    print(f"这种分析方法可以:")
    print(f"  1. 确定传感器的安全工作载荷")
    print(f"  2. 预测不同载荷下的失效模式")
    print(f"  3. 为产品设计提供安全余量建议")
    print(f"  4. 指导材料选择和结构优化")

if __name__ == "__main__":
    main()