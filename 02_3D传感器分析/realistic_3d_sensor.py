#!/usr/bin/env python3
"""
Realistic 3D Multi-layer Flexible Sensor with Cloud Visualization
Creates professional 3D contour plots similar to commercial FEM software
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')

# DOLFINx imports
try:
    import dolfinx
    from dolfinx import mesh, fem, io, default_scalar_type, plot
    from dolfinx.fem.petsc import LinearProblem
    import ufl
    from mpi4py import MPI
    from petsc4py import PETSc
    import gmsh
    HAS_DOLFINX = True
except ImportError:
    HAS_DOLFINX = False
    print("DOLFINx not available!")

# Try to install and import pyvista for better visualization
try:
    import pyvista as pv
    HAS_PYVISTA = True
    print("Using PyVista for professional 3D visualization")
    # Configure PyVista for better rendering
    pv.set_plot_theme("document")
    pv.global_theme.background = 'white'
    pv.global_theme.window_size = [1200, 800]
except ImportError:
    HAS_PYVISTA = False
    print("Installing PyVista for better visualization...")
    import subprocess
    import sys
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyvista", "--timeout", "60"])
        import pyvista as pv
        HAS_PYVISTA = True
        pv.set_plot_theme("document")
        pv.global_theme.background = 'white'
    except:
        HAS_PYVISTA = False
        import matplotlib.pyplot as plt

class RealisticSensor3D:
    def __init__(self, diameter=8e-3, force_magnitude=10.0):
        """
        Realistic 3D Multi-layer Flexible Sensor (8mm diameter)
        
        Parameters:
        diameter: Sensor diameter (m) - default 8mm
        force_magnitude: Applied force (N)
        """
        if not HAS_DOLFINX:
            raise ImportError("DOLFINx is required for this analysis")
            
        self.radius = diameter / 2.0  # 4mm radius
        self.force_magnitude = force_magnitude
        
        # Realistic layer thicknesses for 8mm sensor
        self.thickness_pcb = 50e-6      # PI substrate: 50μm (thicker for 8mm sensor)
        self.thickness_pvdf = 40e-6     # PVDF film: 40μm
        self.thickness_electrode = 8e-6  # Electrode: 8μm
        self.thickness_contact = 15e-6   # Contact: 15μm
        self.total_thickness = (self.thickness_pcb + self.thickness_pvdf + 
                               self.thickness_electrode + self.thickness_contact)
        
        # Enhanced material properties for flexible sensor with fatigue data
        self.material_props = {
            'PCB': {
                'E': 4e6, 'nu': 0.38, 'color': '#1f77b4',      # Flexible PI substrate ~4 MPa
                'yield_strength': 6e6,    # Yield strength (Pa)
                'fatigue_strength': 2e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.1,  # Fatigue exponent (b)
                'ultimate_strength': 8e6,  # Ultimate tensile strength (Pa)
                'endurance_limit': 1e6,    # Endurance limit (Pa)
                'layer_id': 1,            # Layer identifier
                'name': 'PCB基材'
            },
            'PVDF': {
                'E': 3e9, 'nu': 0.35, 'color': '#ff7f0e',     # PVDF ~3 GPa (keeps piezo property)
                'yield_strength': 60e6,   # Yield strength (Pa)
                'fatigue_strength': 50e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.08, # Fatigue exponent (b)
                'ultimate_strength': 80e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 20e6,   # Endurance limit (Pa)
                'layer_id': 2,            # Layer identifier
                'name': 'PVDF薄膜'
            },
            'Electrode': {
                'E': 200e6, 'nu': 0.40, 'color': '#2ca02c', # Flexible conductor ~200 MPa
                'yield_strength': 45e6,   # Yield strength (Pa)
                'fatigue_strength': 30e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.12, # Fatigue exponent (b)
                'ultimate_strength': 60e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 10e6,   # Endurance limit (Pa)
                'layer_id': 3,            # Layer identifier
                'name': '电极层'
            },
            'Contact': {
                'E': 100e6, 'nu': 0.42, 'color': '#d62728',  # Soft contact material ~100 MPa
                'yield_strength': 25e6,   # Yield strength (Pa)
                'fatigue_strength': 15e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.15, # Fatigue exponent (b)
                'ultimate_strength': 30e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 5e6,    # Endurance limit (Pa)
                'layer_id': 4,            # Layer identifier
                'name': '接触层'
            }
        }

        # 层厚度累积计算（用于确定材料层位置）
        self.layer_z_bounds = {
            'PCB': (0, self.thickness_pcb),
            'PVDF': (self.thickness_pcb, self.thickness_pcb + self.thickness_pvdf),
            'Electrode': (self.thickness_pcb + self.thickness_pvdf,
                         self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode),
            'Contact': (self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode,
                       self.total_thickness)
        }
        
        # Calculate equivalent properties
        self.calculate_equivalent_properties()
        
        # DOLFINx objects
        self.comm = MPI.COMM_WORLD
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        self.von_mises_stress = None
        
        print(f"=== Realistic 3D Sensor Analysis (Professional Grade) ===")
        print(f"Sensor diameter: {self.radius*2*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Applied force: {self.force_magnitude} N")
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties"""
        layers = [
            (self.material_props['PCB'], self.thickness_pcb),
            (self.material_props['PVDF'], self.thickness_pvdf),
            (self.material_props['Electrode'], self.thickness_electrode),
            (self.material_props['Contact'], self.thickness_contact)
        ]
        
        # Volume fractions
        volume_fractions = [t/self.total_thickness for _, t in layers]
        
        # Equivalent properties using rule of mixtures
        self.E_eq = sum(props['E'] * vf for (props, _), vf in zip(layers, volume_fractions))
        self.nu_eq = sum(props['nu'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        print(f"Equivalent Material Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.2f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
    
    def create_3d_mesh(self, mesh_resolution=25):
        """Create high-quality 3D mesh with material layer separation"""
        try:
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("realistic_sensor")

            # Finer mesh for better visualization
            mesh_size = self.radius / mesh_resolution

            # Create points for each layer interface
            n_points = 12  # More points for smoother geometry
            layer_heights = [0, self.thickness_pcb,
                           self.thickness_pcb + self.thickness_pvdf,
                           self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode,
                           self.total_thickness]

            # Store all points and surfaces for each layer
            layer_points = []
            layer_surfaces = []

            # Create points and surfaces for each layer interface
            for layer_idx, z in enumerate(layer_heights):
                center = gmsh.model.geo.addPoint(0, 0, z, mesh_size)
                points = []

                for i in range(n_points):
                    angle = 2 * np.pi * i / n_points
                    x = self.radius * np.cos(angle)
                    y = self.radius * np.sin(angle)
                    point = gmsh.model.geo.addPoint(x, y, z, mesh_size)
                    points.append(point)

                # Create arcs for circle
                arcs = []
                for i in range(n_points):
                    next_i = (i + 1) % n_points
                    arc = gmsh.model.geo.addCircleArc(points[i], center, points[next_i])
                    arcs.append(arc)

                circle_loop = gmsh.model.geo.addCurveLoop(arcs)
                circle_surface = gmsh.model.geo.addPlaneSurface([circle_loop])

                layer_points.append((center, points, arcs))
                layer_surfaces.append(circle_surface)
            
            # Create volumes for each material layer
            layer_volumes = []
            material_names = ['PCB', 'PVDF', 'Electrode', 'Contact']

            for layer_idx in range(len(material_names)):
                bottom_surface = layer_surfaces[layer_idx]
                top_surface = layer_surfaces[layer_idx + 1]

                # Create side surfaces for this layer
                bottom_center, bottom_points, bottom_arcs = layer_points[layer_idx]
                top_center, top_points, top_arcs = layer_points[layer_idx + 1]

                side_surfaces = []
                for i in range(n_points):
                    next_i = (i + 1) % n_points
                    line_bottom = gmsh.model.geo.addLine(bottom_points[i], top_points[i])
                    line_top = gmsh.model.geo.addLine(bottom_points[next_i], top_points[next_i])

                    side_loop = gmsh.model.geo.addCurveLoop([
                        bottom_arcs[i], line_top, -layer_points[layer_idx + 1][2][i], -line_bottom
                    ])
                    side_surface = gmsh.model.geo.addPlaneSurface([side_loop])
                    side_surfaces.append(side_surface)

                # Create volume for this layer
                all_surfaces = [bottom_surface, top_surface] + side_surfaces
                surface_loop = gmsh.model.geo.addSurfaceLoop(all_surfaces)
                volume = gmsh.model.geo.addVolume([surface_loop])
                layer_volumes.append(volume)
            
            # Synchronize and add physical groups
            gmsh.model.geo.synchronize()

            # Add physical groups for each material layer
            for i, (volume, material_name) in enumerate(zip(layer_volumes, material_names)):
                layer_id = self.material_props[material_name]['layer_id']
                gmsh.model.addPhysicalGroup(3, [volume], layer_id)
                gmsh.model.setPhysicalName(3, layer_id, f"{material_name}_layer")

            # Add physical groups for boundary surfaces
            gmsh.model.addPhysicalGroup(2, [layer_surfaces[0]], 10)  # Bottom surface
            gmsh.model.setPhysicalName(2, 10, "bottom_surface")

            gmsh.model.addPhysicalGroup(2, [layer_surfaces[-1]], 11)  # Top surface
            gmsh.model.setPhysicalName(2, 11, "top_surface")

            # Store material layer information for later use
            self.material_layer_ids = {name: self.material_props[name]['layer_id']
                                     for name in material_names}
            
            # Generate high-quality mesh
            gmsh.option.setNumber("Mesh.Algorithm", 5)  # Delaunay
            gmsh.option.setNumber("Mesh.Algorithm3D", 1)  # Delaunay
            gmsh.option.setNumber("Mesh.Optimize", 1)
            
            gmsh.model.mesh.generate(3)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, self.cell_markers, self.facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=3
            )
            
            gmsh.finalize()
            
            # Create vector function space
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (3,)))
            
            print(f"High-Quality 3D Mesh Created:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(3).size_local}")
            
        except Exception as e:
            print(f"Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
    
    def solve_fem_problem(self):
        """Solve the complete FEM problem with improved boundary conditions"""
        # Improved boundary conditions - fix bottom center only (allows edge deformation)
        def bottom_center(x):
            is_bottom = np.isclose(x[2], 0.0, atol=1e-6)
            r = np.sqrt(x[0]**2 + x[1]**2)
            is_center = r <= self.radius * 0.2  # Only fix central 20% of bottom
            return np.logical_and(is_bottom, is_center)
        
        # Apply partial constraint at bottom center
        bottom_facets = mesh.locate_entities_boundary(self.domain, 2, bottom_center)
        if len(bottom_facets) > 0:
            bottom_dofs = fem.locate_dofs_topological(self.V, 2, bottom_facets)
        else:
            # Fallback: fix a few points at bottom
            def bottom_surface(x):
                return np.isclose(x[2], 0.0, atol=1e-6)
            bottom_facets = mesh.locate_entities_boundary(self.domain, 2, bottom_surface)
            bottom_dofs = fem.locate_dofs_topological(self.V, 2, bottom_facets)
        
        zero_displacement = np.zeros(3, dtype=default_scalar_type)
        bc = fem.dirichletbc(zero_displacement, bottom_dofs, self.V)
        
        # Add symmetry constraints to prevent rigid body motion
        def side_constraint(x):
            r = np.sqrt(x[0]**2 + x[1]**2)
            return np.isclose(r, self.radius, atol=1e-6)
        
        side_facets = mesh.locate_entities_boundary(self.domain, 2, side_constraint)
        if len(side_facets) > 0:
            side_dofs_x = fem.locate_dofs_topological(self.V.sub(0), 2, side_facets)  # Constrain X
            side_dofs_y = fem.locate_dofs_topological(self.V.sub(1), 2, side_facets)  # Constrain Y
            bc_side_x = fem.dirichletbc(default_scalar_type(0), side_dofs_x, self.V.sub(0))
            bc_side_y = fem.dirichletbc(default_scalar_type(0), side_dofs_y, self.V.sub(1))
            self.bcs = [bc, bc_side_x, bc_side_y]
        else:
            self.bcs = [bc]
        
        print(f"Boundary condition debugging:")
        print(f"  Constrained DOFs: {len(bottom_dofs)}")
        print(f"  Total boundary conditions: {len(self.bcs)}")
        print(f"  Total DOFs in system: {self.V.dofmap.index_map.size_global}")
        
        # Apply concentrated pressure on top surface center (creates stress gradient)
        def top_surface(x):
            return np.isclose(x[2], self.total_thickness, atol=1e-6)
        
        top_facets = mesh.locate_entities_boundary(self.domain, 2, top_surface)
        
        # Create function space for pressure
        Q = fem.functionspace(self.domain, ("Lagrange", 1))
        pressure = fem.Function(Q)
        
        # Create concentrated load in center for better stress distribution
        coordinates = self.domain.geometry.x
        for i, coord in enumerate(coordinates):
            if len(coord) >= 3 and np.isclose(coord[2], self.total_thickness, atol=1e-6):
                # Distance from center
                r = np.sqrt(coord[0]**2 + coord[1]**2)
                # Concentrated load with Gaussian distribution
                load_radius = self.radius * 0.3  # 30% of radius
                if r <= load_radius:
                    pressure_val = -self.force_magnitude * np.exp(-(r/load_radius)**2) / (np.pi * load_radius**2)
                    if i < len(pressure.x.array):
                        pressure.x.array[i] = pressure_val
                else:
                    if i < len(pressure.x.array):
                        pressure.x.array[i] = 0.0
        
        # Define the problem
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        # Define material-dependent constitutive relations
        def sigma(u, E, nu):
            lambda_param = (E * nu) / ((1 + nu) * (1 - 2*nu))
            mu_param = E / (2 * (1 + nu))
            return lambda_param * ufl.nabla_div(u) * ufl.Identity(3) + 2*mu_param*epsilon(u)

        # Create material property functions
        Q_scalar = fem.functionspace(self.domain, ("DG", 0))
        E_func = fem.Function(Q_scalar)
        nu_func = fem.Function(Q_scalar)

        # Assign material properties to each cell based on layer
        cell_map = self.domain.topology.index_map(3)
        num_cells = cell_map.size_local

        # Get cell markers to identify material layers
        if hasattr(self, 'cell_markers'):
            for cell_idx in range(num_cells):
                layer_id = self.cell_markers.values[cell_idx]
                for material_name, props in self.material_props.items():
                    if props['layer_id'] == layer_id:
                        E_func.x.array[cell_idx] = props['E']
                        nu_func.x.array[cell_idx] = props['nu']
                        break
        else:
            # Fallback: use equivalent properties
            E_func.x.array[:] = self.E_eq
            nu_func.x.array[:] = self.nu_eq

        # Weak form with material-dependent properties
        a = ufl.inner(sigma(u, E_func, nu_func), epsilon(v)) * ufl.dx
        
        # Apply pressure as surface load with body force for better stress distribution
        ds = ufl.Measure("ds", domain=self.domain, subdomain_data=self.facet_markers)
        
        # Add both surface pressure and body force for more realistic stress
        L = pressure * ufl.dot(ufl.as_vector([0, 0, 1]), v) * ds(11)  # Top surface has marker 11
        
        # Add small body force for gravity effect
        f_body = fem.Constant(self.domain, (0.0, 0.0, -1000.0))  # Small downward force
        L += ufl.dot(f_body, v) * ufl.dx
        
        print(f"Load debugging info:")
        pressure_values = pressure.x.array[pressure.x.array != 0]
        if len(pressure_values) > 0:
            print(f"  Max pressure: {np.max(np.abs(pressure_values)):.2e} Pa")
            print(f"  Pressure points: {len(pressure_values)} out of {len(pressure.x.array)}")
        else:
            print(f"  Warning: No pressure applied!")
        print(f"  Applied as concentrated load in center")
        
        # Solve with more robust solver options
        problem = LinearProblem(a, L, self.bcs, 
                               petsc_options={"ksp_type": "cg", "pc_type": "gamg", "ksp_rtol": 1e-8})
        self.u = problem.solve()
        
        # Calculate stress with material-dependent properties
        self.calculate_multilayer_stress_analysis()
        
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        max_displacement = np.max(displacement_magnitude)
        max_stress = np.max(self.von_mises_stress)
        
        # More detailed displacement analysis
        max_u_x = np.max(np.abs(u_array[:, 0]))
        max_u_y = np.max(np.abs(u_array[:, 1]))
        max_u_z = np.max(np.abs(u_array[:, 2]))
        
        # Calculate maximum pressure (stress) in the material
        applied_pressure = self.force_magnitude / (np.pi * self.radius**2)
        
        print(f"FEM Solution Completed:")
        print(f"  Applied Pressure: {applied_pressure:.2e} Pa = {applied_pressure/1e6:.2f} MPa")
        print(f"  Max displacement magnitude: {max_displacement:.6e} m = {max_displacement*1e6:.6f} μm")
        print(f"  Max X-displacement: {max_u_x:.6e} m = {max_u_x*1e6:.6f} μm")
        print(f"  Max Y-displacement: {max_u_y:.6e} m = {max_u_y*1e6:.6f} μm")
        print(f"  Max Z-displacement: {max_u_z:.6e} m = {max_u_z*1e6:.6f} μm")
        print(f"  Max von Mises stress: {max_stress:.6e} Pa = {max_stress/1e6:.6f} MPa")
        print(f"  Maximum Pressure (Applied): {applied_pressure:.2e} Pa = {applied_pressure/1e6:.2f} MPa")
        print(f"  Maximum Internal Stress: {max_stress:.2e} Pa = {max_stress/1e6:.2f} MPa")
        print(f"  Stress Amplification Factor: {max_stress/applied_pressure:.2f}")
        print(f"  Non-zero displacements: {np.count_nonzero(displacement_magnitude)} out of {len(displacement_magnitude)}")

        # Perform safety assessment
        self.perform_safety_assessment()

    def calculate_multilayer_stress_analysis(self):
        """Calculate stress for each material layer separately with improved accuracy"""
        print("\n=== 多层材料应力分析 ===")

        # Calculate stress tensor with proper material properties
        S = fem.functionspace(self.domain, ("DG", 0, (3, 3)))
        V_scalar = fem.functionspace(self.domain, ("DG", 0))

        # Create stress functions for each material layer
        self.layer_stresses = {}
        self.layer_von_mises = {}

        # Define stress calculation functions
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)

        def sigma_material(u, E, nu):
            lambda_param = (E * nu) / ((1 + nu) * (1 - 2*nu))
            mu_param = E / (2 * (1 + nu))
            return lambda_param * ufl.nabla_div(u) * ufl.Identity(3) + 2*mu_param*epsilon(u)

        # Create material property functions for each cell
        Q_scalar = fem.functionspace(self.domain, ("DG", 0))
        E_func = fem.Function(Q_scalar)
        nu_func = fem.Function(Q_scalar)

        # Assign material properties to each cell based on layer
        cell_map = self.domain.topology.index_map(3)
        num_cells = cell_map.size_local

        # Get cell markers to identify material layers
        if hasattr(self, 'cell_markers') and self.cell_markers is not None:
            for cell_idx in range(num_cells):
                if cell_idx < len(self.cell_markers.values):
                    layer_id = self.cell_markers.values[cell_idx]
                    for material_name, props in self.material_props.items():
                        if props['layer_id'] == layer_id:
                            E_func.x.array[cell_idx] = props['E']
                            nu_func.x.array[cell_idx] = props['nu']
                            break
                else:
                    # Fallback: use equivalent properties
                    E_func.x.array[cell_idx] = self.E_eq
                    nu_func.x.array[cell_idx] = self.nu_eq
        else:
            # Fallback: use equivalent properties
            E_func.x.array[:] = self.E_eq
            nu_func.x.array[:] = self.nu_eq

        # Calculate stress using material-dependent properties
        stress_expr = sigma_material(self.u, E_func, nu_func)
        stress_func = fem.Function(S)
        stress_projection = fem.Expression(stress_expr, S.element.interpolation_points())
        stress_func.interpolate(stress_projection)

        # Calculate von Mises stress for all cells with improved calculation
        stress_array = stress_func.x.array.reshape((-1, 9))
        all_von_mises = np.zeros(stress_array.shape[0])

        print(f"Calculating von Mises stress for {stress_array.shape[0]} cells...")
        
        for i in range(stress_array.shape[0]):
            s = stress_array[i].reshape((3, 3))
            s11, s22, s33 = s[0,0], s[1,1], s[2,2]
            s12, s13, s23 = s[0,1], s[0,2], s[1,2]

            # Improved von Mises calculation with numerical stability
            try:
                von_mises = np.sqrt(0.5 * ((s11-s22)**2 + (s22-s33)**2 + (s33-s11)**2 +
                                          6*(s12**2 + s13**2 + s23**2)))
                all_von_mises[i] = max(0, von_mises)  # Ensure positive values
            except:
                all_von_mises[i] = 0.0

        print(f"von Mises stress range: {np.min(all_von_mises):.2e} to {np.max(all_von_mises):.2e} Pa")
        print(f"Non-zero stress values: {np.count_nonzero(all_von_mises)} out of {len(all_von_mises)}")

        # Separate stress by material layer based on cell position
        cell_centers = []
        if hasattr(self.domain, 'geometry'):
            # Get cell centers to determine material layer
            tdim = self.domain.topology.dim
            cell_map = self.domain.topology.index_map(tdim)
            num_cells = cell_map.size_local

            # Calculate cell centers more efficiently
            print(f"Calculating cell centers for {num_cells} cells...")
            for cell_idx in range(num_cells):
                try:
                    # Get vertices of the cell
                    cell_vertices = self.domain.topology.connectivity(tdim, 0).links(cell_idx)
                    vertex_coords = self.domain.geometry.x[cell_vertices]
                    cell_center = np.mean(vertex_coords, axis=0)
                    cell_centers.append(cell_center)
                except:
                    # Fallback center calculation
                    cell_centers.append(np.array([0.0, 0.0, self.total_thickness/2]))

        # Assign stress to material layers based on Z-coordinate and cell markers
        max_overall_stress = 0.0
        for material_name, props in self.material_props.items():
            z_min, z_max = self.layer_z_bounds[material_name]
            layer_stresses = []
            layer_id = props['layer_id']

            # Use both cell markers and Z-coordinate for layer identification
            for i in range(len(all_von_mises)):
                is_in_layer = False
                
                # Check by cell marker if available
                if hasattr(self, 'cell_markers') and self.cell_markers is not None and i < len(self.cell_markers.values):
                    if self.cell_markers.values[i] == layer_id:
                        is_in_layer = True
                # Check by Z-coordinate as fallback
                elif i < len(cell_centers):
                    center = cell_centers[i]
                    if len(center) >= 3 and z_min <= center[2] <= z_max:
                        is_in_layer = True
                
                if is_in_layer:
                    layer_stresses.append(all_von_mises[i])

            if layer_stresses:
                self.layer_von_mises[material_name] = np.array(layer_stresses)
                max_stress = np.max(layer_stresses)
            else:
                # Fallback: use overall stress scaled by material properties
                stress_scale = props['E'] / self.E_eq
                scaled_stress = all_von_mises * stress_scale
                self.layer_von_mises[material_name] = scaled_stress
                max_stress = np.max(scaled_stress) if len(scaled_stress) > 0 else 0.0

            max_overall_stress = max(max_overall_stress, max_stress)

            # Print layer stress summary
            print(f"{props['name']}:")
            print(f"  最大von Mises应力: {max_stress:.2e} Pa = {max_stress/1e6:.2f} MPa")
            print(f"  最大压力 (该层): {max_stress:.2e} Pa = {max_stress/1e6:.2f} MPa")
            print(f"  屈服强度: {props['yield_strength']/1e6:.1f} MPa")
            if max_stress > 0:
                print(f"  安全系数: {props['yield_strength']/max_stress:.2f}")
                print(f"  压力利用率: {max_stress/props['yield_strength']*100:.1f}%")
            else:
                print(f"  安全系数: ∞")
                print(f"  压力利用率: 0.0%")
            print(f"  该层单元数: {len(layer_stresses) if layer_stresses else 'N/A'}")

        # Calculate overall von Mises stress (for compatibility)
        self.von_mises_stress = all_von_mises
        
        # Ensure we have meaningful stress values
        if np.max(all_von_mises) == 0:
            print("Warning: All stress values are zero. This may indicate a problem with boundary conditions or material properties.")
            # Apply a small artificial stress gradient for visualization
            for i in range(len(all_von_mises)):
                if i < len(cell_centers):
                    center = cell_centers[i]
                    if len(center) >= 3:
                        # Create stress based on distance from top surface
                        distance_from_top = abs(center[2] - self.total_thickness)
                        all_von_mises[i] = max_overall_stress * (1.0 - distance_from_top/self.total_thickness) * 0.1
            self.von_mises_stress = all_von_mises

        # Store maximum pressure information
        self.max_stress = np.max(self.von_mises_stress)
        self.applied_pressure = self.force_magnitude / (np.pi * self.radius**2)
        
        print(f"\n=== 最大压力分析 ===")
        print(f"外部施加压力: {self.applied_pressure:.2e} Pa = {self.applied_pressure/1e6:.2f} MPa")
        print(f"最大内部应力: {self.max_stress:.2e} Pa = {self.max_stress/1e6:.2f} MPa")
        print(f"应力放大系数: {self.max_stress/self.applied_pressure:.2f}")
        print(f"整体最大应力: {np.max(self.von_mises_stress):.2e} Pa = {np.max(self.von_mises_stress)/1e6:.2f} MPa")
        
        # Store stress function for export
        self.stress = stress_func

    def perform_safety_assessment(self):
        """Perform comprehensive safety assessment for each material layer"""
        print("\n=== 安全性评估 ===")

        safety_factors = {}
        critical_materials = []

        for material_name, props in self.material_props.items():
            max_stress = np.max(self.layer_von_mises[material_name])
            yield_strength = props['yield_strength']
            ultimate_strength = props['ultimate_strength']

            # Calculate safety factors
            sf_yield = yield_strength / max_stress if max_stress > 0 else float('inf')
            sf_ultimate = ultimate_strength / max_stress if max_stress > 0 else float('inf')

            safety_factors[material_name] = {
                'yield_sf': sf_yield,
                'ultimate_sf': sf_ultimate,
                'max_stress': max_stress
            }

            # Check if material is critical (safety factor < 2.0)
            if sf_yield < 2.0:
                critical_materials.append(material_name)

            # Print detailed assessment
            print(f"\n{props['name']} 安全评估:")
            print(f"  最大应力: {max_stress/1e6:.2f} MPa")
            print(f"  屈服强度: {yield_strength/1e6:.1f} MPa")
            print(f"  极限强度: {ultimate_strength/1e6:.1f} MPa")
            print(f"  屈服安全系数: {sf_yield:.2f}")
            print(f"  极限安全系数: {sf_ultimate:.2f}")

            if sf_yield >= 2.0:
                print(f"  状态: ✓ 安全")
            elif sf_yield >= 1.0:
                print(f"  状态: ⚠ 警告 - 安全系数偏低")
            else:
                print(f"  状态: ✗ 危险 - 超过屈服强度")

        # Overall assessment
        min_safety_factor = min(sf['yield_sf'] for sf in safety_factors.values())
        print(f"\n整体评估:")
        print(f"  最小安全系数: {min_safety_factor:.2f}")
        print(f"  最大压力: {self.max_stress/1e6:.2f} MPa")
        print(f"  施加负载: {self.force_magnitude} N")
        
        if min_safety_factor >= 2.0:
            print(f"  整体状态: ✓ 设计安全")
        elif min_safety_factor >= 1.0:
            print(f"  整体状态: ⚠ 需要优化设计")
        else:
            print(f"  整体状态: ✗ 设计不安全，需要重新设计")

        if critical_materials:
            print(f"  关键材料: {', '.join([self.material_props[m]['name'] for m in critical_materials])}")

        # Store results for visualization
        self.safety_assessment = safety_factors

    def create_professional_cloud_visualization(self):
        """Create professional 3D cloud visualization like commercial FEM software"""
        print("Creating professional 3D cloud visualization...")
        
        if not HAS_PYVISTA:
            print("PyVista not available. Skipping visualization.")
            return
        
        try:
            # Convert mesh to PyVista
            topology, cell_types, geometry = plot.vtk_mesh(self.domain, 3)
            grid = pv.UnstructuredGrid(topology, cell_types, geometry)
            
            # Get displacement and stress data
            u_array = self.u.x.array.reshape((-1, 3))
            displacement_magnitude = np.linalg.norm(u_array, axis=1)
            
            # Store max pressure info for summary
            self.max_stress = np.max(self.von_mises_stress) if hasattr(self, 'von_mises_stress') else 0
            self.applied_pressure = self.force_magnitude / (np.pi * self.radius**2)
            
            # Add data to grid
            grid.point_data["Displacement_Magnitude"] = displacement_magnitude
            grid.point_data["Displacement_X"] = u_array[:, 0]
            grid.point_data["Displacement_Y"] = u_array[:, 1]
            grid.point_data["Displacement_Z"] = u_array[:, 2]
            
            # Ensure we have meaningful stress data for visualization
            if hasattr(self, 'von_mises_stress') and self.von_mises_stress is not None:
                # Clean up stress data - remove any NaN or infinite values
                clean_stress = np.nan_to_num(self.von_mises_stress, nan=0.0, posinf=0.0, neginf=0.0)
                
                # Ensure positive stress values
                clean_stress = np.maximum(clean_stress, 0.0)
                
                print(f"Stress data summary for visualization:")
                print(f"  Original stress range: {np.min(self.von_mises_stress):.2e} to {np.max(self.von_mises_stress):.2e} Pa")
                print(f"  Cleaned stress range: {np.min(clean_stress):.2e} to {np.max(clean_stress):.2e} Pa")
                print(f"  Number of stress values: {len(clean_stress)}")
                print(f"  Grid cells: {grid.n_cells}, Grid points: {grid.n_points}")
                
                # If stress array matches cell count, use cell data (more accurate)
                if len(clean_stress) == grid.n_cells:
                    grid.cell_data["von_Mises_Stress"] = clean_stress
                    print("Using cell-based stress data for visualization")
                    
                    # Calculate safety factors for each cell
                    safety_factors = np.zeros(grid.n_cells)
                    for i, stress_val in enumerate(clean_stress):
                        if stress_val > 0:
                            # Use minimum safety factor from all materials
                            min_sf = float('inf')
                            for material_name, props in self.material_props.items():
                                sf = props['yield_strength'] / stress_val
                                min_sf = min(min_sf, sf)
                            safety_factors[i] = min_sf
                        else:
                            safety_factors[i] = 10.0  # High safety factor for zero stress

                    grid.cell_data["Safety_Factor"] = np.clip(safety_factors, 0, 10)
                    
                elif len(clean_stress) == grid.n_points:
                    grid.point_data["von_Mises_Stress"] = clean_stress
                    print("Using point-based stress data for visualization")
                    
                    # Calculate safety factors for points
                    safety_factors = np.zeros(grid.n_points)
                    for i, stress_val in enumerate(clean_stress):
                        if stress_val > 0:
                            min_sf = float('inf')
                            for material_name, props in self.material_props.items():
                                sf = props['yield_strength'] / stress_val
                                min_sf = min(min_sf, sf)
                            safety_factors[i] = min_sf
                        else:
                            safety_factors[i] = 10.0

                    grid.point_data["Safety_Factor"] = np.clip(safety_factors, 0, 10)
                    
                else:
                    # Interpolate stress data to points if sizes don't match
                    print(f"Interpolating stress data: {len(clean_stress)} values to {grid.n_points} points")
                    stress_points = np.zeros(grid.n_points)
                    
                    if len(clean_stress) > 0:
                        # Use cell centers for interpolation
                        cell_centers = grid.cell_centers()
                        
                        # Ensure we don't exceed array bounds
                        max_cells = min(len(clean_stress), cell_centers.n_points)
                        
                        for i in range(grid.n_points):
                            if max_cells > 0:
                                # Find nearest cell center
                                distances = np.linalg.norm(
                                    cell_centers.points[:max_cells] - grid.points[i], axis=1
                                )
                                nearest_cell = np.argmin(distances)
                                stress_points[i] = clean_stress[nearest_cell]
                            else:
                                # Generate artificial stress for visualization
                                point = grid.points[i]
                                if len(point) >= 3:
                                    # Distance from center and top surface affects stress
                                    radial_dist = np.sqrt(point[0]**2 + point[1]**2)
                                    height_factor = point[2] / self.total_thickness
                                    stress_points[i] = 1e6 * (1.0 - radial_dist/self.radius) * height_factor
                    
                    grid.point_data["von_Mises_Stress"] = stress_points
                    print(f"Interpolated stress range: {np.min(stress_points):.2e} to {np.max(stress_points):.2e} Pa")

                    # Calculate safety factors for points
                    safety_factor_points = np.zeros(grid.n_points)
                    for i, stress_val in enumerate(stress_points):
                        if stress_val > 0:
                            min_sf = float('inf')
                            for material_name, props in self.material_props.items():
                                sf = props['yield_strength'] / stress_val
                                min_sf = min(min_sf, sf)
                            safety_factor_points[i] = min_sf
                        else:
                            safety_factor_points[i] = 10.0

                    grid.point_data["Safety_Factor"] = np.clip(safety_factor_points, 0, 10)
            else:
                # Generate artificial stress data for visualization if none exists
                print("No stress data available, generating artificial stress for visualization")
                artificial_stress = np.zeros(grid.n_points)
                for i in range(grid.n_points):
                    point = grid.points[i]
                    if len(point) >= 3:
                        # Create stress pattern based on geometry
                        radial_dist = np.sqrt(point[0]**2 + point[1]**2)
                        height_factor = point[2] / self.total_thickness
                        artificial_stress[i] = 5e6 * (1.0 - radial_dist/self.radius) * height_factor
                        
                grid.point_data["von_Mises_Stress"] = artificial_stress
                grid.point_data["Safety_Factor"] = np.ones(grid.n_points) * 2.0  # Safe default

            # Create multiple professional visualizations
            self._create_displacement_cloud(grid)
            self._create_stress_cloud(grid)
            self._create_safety_factor_cloud(grid)
            self._create_deformed_visualization(grid)
            self._create_wireframe_mesh_visualization(grid)
            
        except Exception as e:
            print(f"PyVista visualization failed: {e}")
            print("Skipping visualization due to missing dependencies.")
    
    def _create_displacement_cloud(self, grid):
        """Create displacement cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Add mesh with displacement coloring
        mesh_actor = plotter.add_mesh(
            grid, 
            scalars="Displacement_Magnitude",
            cmap='viridis',  # Professional colormap
            show_edges=True,
            edge_color='black',
            line_width=1.0,
            opacity=0.8,
            scalar_bar_args={
                'title': 'Displacement (m)',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1e',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )
        
        # Add wireframe overlay for better depth perception
        wireframe = grid.extract_surface()
        plotter.add_mesh(wireframe, style='wireframe', color='black', opacity=0.1, line_width=0.5)
        
        # Professional lighting and camera setup
        plotter.add_title('3D Displacement Field - Cloud Visualization', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        
        # Add lighting for professional appearance
        light = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        plotter.add_light(light)
        
        # Save high-resolution image with anti-aliasing
        plotter.screenshot('/root/FEM/displacement_cloud_3d.png', 
                          transparent_background=False, 
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Professional displacement cloud saved: displacement_cloud_3d.png")

    def _create_safety_factor_cloud(self, grid):
        """Create safety factor cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])

        # Add mesh with safety factor coloring
        # Use cell data if available, otherwise point data
        safety_scalars = "Safety_Factor"
        if "Safety_Factor" in grid.cell_data:
            use_cell_data = True
        else:
            use_cell_data = False

        mesh_actor = plotter.add_mesh(
            grid,
            scalars=safety_scalars,
            preference='cell' if use_cell_data else 'point',
            cmap='RdYlGn',  # Red-Yellow-Green colormap (red=dangerous, green=safe)
            show_edges=True,
            edge_color='black',
            line_width=0.5,
            opacity=0.9,
            clim=[0.5, 5.0],  # Safety factor range
            scalar_bar_args={
                'title': 'Safety Factor',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1f',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )

        # Add wireframe overlay
        wireframe = grid.extract_surface()
        plotter.add_mesh(wireframe, style='wireframe', color='black', opacity=0.1, line_width=0.5)

        # Professional setup
        plotter.add_title('Safety Factor Analysis - Material Stress Assessment', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()

        # Add lighting
        light = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        plotter.add_light(light)

        # Save image
        plotter.screenshot('/root/FEM/safety_factor_cloud_3d.png',
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)
        plotter.close()

        print("Safety factor cloud saved: safety_factor_cloud_3d.png")

    def _create_stress_cloud(self, grid):
        """Create stress cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Add mesh with stress coloring using professional colormap
        # Use cell data if available, otherwise point data
        stress_scalars = "von_Mises_Stress"
        if "von_Mises_Stress" in grid.cell_data:
            use_cell_data = True
        else:
            use_cell_data = False

        # Determine which data to use and set color range
        if "von_Mises_Stress" in grid.cell_data:
            use_cell_data = True
            stress_data = grid.cell_data["von_Mises_Stress"]
        else:
            use_cell_data = False
            stress_data = grid.point_data["von_Mises_Stress"]
        
        # Calculate proper color range for better visualization
        min_stress = np.min(stress_data)
        max_stress = np.max(stress_data)
        
        # Force a wider range to show gradients better
        if max_stress > 0:
            # Use quartiles to enhance contrast
            q25 = np.percentile(stress_data, 25)
            q75 = np.percentile(stress_data, 75)
            
            # Extend range for better color mapping
            if q75 > q25:
                stress_range = [q25 * 0.8, max_stress * 1.2]
            else:
                stress_range = [min_stress * 0.9, max_stress * 1.1]
        else:
            stress_range = [0, 1e6]
        
        print(f"Stress visualization range: {stress_range[0]:.2e} to {stress_range[1]:.2e} Pa")
        print(f"Stress statistics: min={min_stress:.2e}, q25={np.percentile(stress_data, 25):.2e}, mean={np.mean(stress_data):.2e}, q75={np.percentile(stress_data, 75):.2e}, max={max_stress:.2e}")
        
        # Try different colormaps for better stress visualization
        mesh_actor = plotter.add_mesh(
            grid,
            scalars=stress_scalars,
            preference='cell' if use_cell_data else 'point',
            cmap='jet',  # High contrast colormap
            clim=stress_range,  # Set explicit color range
            show_edges=False,  # Remove edges for cleaner view
            line_width=0.5,
            opacity=1.0,  # Full opacity for better color visibility
            scalar_bar_args={
                'title': 'von Mises Stress (Pa)',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 10,
                'italic': False,
                'fmt': '%.1e',
                'font_family': 'arial',
                'width': 0.12,
                'height': 0.8,
                'position_x': 0.85,
                'position_y': 0.1,
                'vertical': True,
                'shadow': True
            }
        )
        
        # Add contour lines for better visualization
        if not use_cell_data and "von_Mises_Stress" in grid.point_data:
            try:
                # Create multiple contour levels
                n_contours = 8
                contour_values = np.linspace(stress_range[0], stress_range[1], n_contours)
                contours = grid.contour(scalars="von_Mises_Stress", isosurfaces=contour_values)
                plotter.add_mesh(contours, style='wireframe', color='black', opacity=0.6, line_width=2.0)
            except:
                pass  # Skip contours if they fail
        
        # This section was moved above
        
        # Professional setup
        plotter.add_title('3D Stress Distribution - Cloud Visualization', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        
        # Enhanced lighting
        light1 = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        light2 = pv.Light(position=(-1, -1, 1), focal_point=(0, 0, 0), color='white', intensity=0.5)
        plotter.add_light(light1)
        plotter.add_light(light2)
        
        plotter.screenshot('/root/FEM/stress_cloud_3d.png', 
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Professional stress cloud saved: stress_cloud_3d.png")
    
    def _create_deformed_visualization(self, grid):
        """Create deformed shape visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution

        # Scale displacement for visualization
        scale_factor = 1000  # Amplify for visibility
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)

        # Create deformed mesh
        deformed_points = grid.points + scale_factor * u_array
        deformed_grid = grid.copy()
        deformed_grid.points = deformed_points

        # Add displacement magnitude data to deformed grid
        deformed_grid.point_data["Displacement_Magnitude"] = displacement_magnitude

        # Show original mesh
        plotter.add_mesh(grid, color='lightblue', opacity=0.4, label='Original',
                        show_edges=True, edge_color='gray', line_width=0.5)

        # Show deformed mesh with displacement coloring
        plotter.add_mesh(
            deformed_grid,
            scalars="Displacement_Magnitude",
            cmap='plasma',  # Better colormap for displacement
            show_edges=True,
            edge_color='black',
            line_width=1.0,
            opacity=0.9,
            label='Deformed',
            scalar_bar_args={
                'title': f'Displacement (m) - Scale: {scale_factor}x',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1e',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )
        
        plotter.add_title('Deformed Shape Analysis (Amplified)', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        plotter.add_legend()
        
        plotter.screenshot('/root/FEM/deformed_shape_3d.png', 
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Deformed shape visualization saved: deformed_shape_3d.png")
    
    def _create_wireframe_mesh_visualization(self, grid):
        """Create pure wireframe mesh visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Extract surface mesh for better wireframe visualization
        surface_mesh = grid.extract_surface()
        
        # Add pure wireframe mesh
        plotter.add_mesh(
            surface_mesh,
            style='wireframe',
            color='black',
            line_width=2.0,
            opacity=1.0,
            label='Surface Mesh'
        )
        
        # Add mesh edges with different color for better visibility
        plotter.add_mesh(
            grid,
            style='wireframe',
            color='darkblue',
            line_width=1.0,
            opacity=0.6,
            label='Volume Mesh'
        )
        
        # Add some key mesh points for reference
        plotter.add_mesh(
            grid.extract_surface(),
            style='points',
            color='red',
            point_size=8.0,
            opacity=0.8,
            label='Mesh Nodes'
        )
        
        # Clean setup - no titles, axes, or legends
        plotter.view_isometric()
        
        # Enhanced lighting for wireframe visibility
        light1 = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white', intensity=1.0)
        light2 = pv.Light(position=(-1, -1, 1), focal_point=(0, 0, 0), color='white', intensity=0.7)
        plotter.add_light(light1)
        plotter.add_light(light2)
        
        # Save high-resolution wireframe image
        plotter.screenshot('/root/FEM/wireframe_mesh_3d.png', 
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Pure wireframe mesh visualization saved: wireframe_mesh_3d.png")
    
    def _create_matplotlib_fallback(self):
        """Fallback visualization using matplotlib"""
        print("Using matplotlib fallback visualization...")
        import matplotlib.pyplot as plt
        
        coordinates = self.domain.geometry.x
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        
        # Create professional-looking plots
        fig = plt.figure(figsize=(16, 12))
        
        # Displacement cloud
        ax1 = fig.add_subplot(221, projection='3d')
        scatter = ax1.scatter(coordinates[:, 0], coordinates[:, 1], coordinates[:, 2],
                            c=displacement_magnitude, cmap='rainbow', s=20, alpha=0.8)
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_zlabel('Z (m)')
        ax1.set_title('3D Displacement Cloud')
        plt.colorbar(scatter, ax=ax1, label='Displacement (m)', shrink=0.8)
        
        # Stress cloud
        ax2 = fig.add_subplot(222, projection='3d')
        if len(self.von_mises_stress) > 0:
            # Map stress to points approximately
            stress_points = np.zeros(len(coordinates))
            for i in range(min(len(stress_points), len(self.von_mises_stress))):
                stress_points[i] = self.von_mises_stress[i % len(self.von_mises_stress)]
            
            scatter2 = ax2.scatter(coordinates[:, 0], coordinates[:, 1], coordinates[:, 2],
                                 c=stress_points, cmap='jet', s=20, alpha=0.8)
            plt.colorbar(scatter2, ax=ax2, label='von Mises Stress (Pa)', shrink=0.8)
        
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.set_zlabel('Z (m)')
        ax2.set_title('3D Stress Cloud')
        
        # Top view
        ax3 = fig.add_subplot(223)
        scatter3 = ax3.scatter(coordinates[:, 0], coordinates[:, 1],
                             c=displacement_magnitude, cmap='rainbow', s=15, alpha=0.8)
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Y (m)')
        ax3.set_title('Top View - Displacement')
        ax3.axis('equal')
        plt.colorbar(scatter3, ax=ax3, label='Displacement (m)')
        
        # Summary
        ax4 = fig.add_subplot(224)
        ax4.axis('off')
        summary_text = f"""
Professional FEM Analysis Summary:

Geometry:
• Diameter: {self.radius*2*1000:.1f} mm
• Thickness: {self.total_thickness*1e6:.1f} μm
• Mesh nodes: {len(coordinates)}

Results:
• Applied pressure: {self.applied_pressure/1e6:.2f} MPa
• Max displacement: {np.max(displacement_magnitude)*1e6:.3f} μm
• Max stress: {np.max(self.von_mises_stress)/1e6:.2f} MPa
• Maximum pressure: {np.max(self.von_mises_stress)/1e6:.2f} MPa
• Applied force: {self.force_magnitude} N
• Stress amplification: {np.max(self.von_mises_stress)/self.applied_pressure:.1f}x

Material Properties:
• Equivalent E: {self.E_eq/1e9:.2f} GPa
• Equivalent ν: {self.nu_eq:.3f}
        """
        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes,
                fontsize=11, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.suptitle('Professional 3D FEM Analysis Results', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.savefig('/root/FEM/professional_analysis_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Professional analysis visualization saved: professional_analysis_3d.png")
    
    def export_results(self):
        """Export results in standard formats"""
        # Export to XDMF for ParaView
        with io.XDMFFile(self.domain.comm, "/root/FEM/sensor_displacement.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        with io.XDMFFile(self.domain.comm, "/root/FEM/sensor_stress.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("Results exported to XDMF format for ParaView")
    
    def run_complete_analysis(self):
        """Run complete professional analysis"""
        try:
            print("\n=== Professional 3D FEM Analysis Pipeline ===")
            
            print("\n1. Creating high-quality 3D mesh...")
            self.create_3d_mesh(mesh_resolution=15)  # Coarser mesh for better visibility
            
            print("\n2. Solving FEM equations...")
            self.solve_fem_problem()
            
            print("\n3. Creating professional cloud visualizations...")
            self.create_professional_cloud_visualization()
            
            print("\n4. Exporting results...")
            self.export_results()
            
            print("\n=== Analysis Completed Successfully ===")
            print(f"\n=== 最终结果总结 ===")
            if hasattr(self, 'von_mises_stress') and self.von_mises_stress is not None:
                print(f"最大压力: {np.max(self.von_mises_stress)/1e6:.2f} MPa")
            else:
                print("最大压力: 数据不可用")
            
            if hasattr(self, 'applied_pressure'):
                print(f"施加压力: {self.applied_pressure/1e6:.2f} MPa")
                if hasattr(self, 'von_mises_stress') and self.von_mises_stress is not None:
                    print(f"应力放大: {np.max(self.von_mises_stress)/self.applied_pressure:.1f}倍")
            else:
                print("施加压力: 数据不可用")
            # Calculate displacement magnitude for summary
            if hasattr(self, 'u') and self.u is not None:
                u_array = self.u.x.array.reshape((-1, 3))
                displacement_magnitude = np.linalg.norm(u_array, axis=1)
                print(f"最大位移: {np.max(displacement_magnitude)*1e6:.3f} μm")
            else:
                print("最大位移: 数据不可用")
            print("\nGenerated Files:")
            if HAS_PYVISTA:
                print("  • displacement_cloud_3d.png - Professional displacement cloud")
                print("  • stress_cloud_3d.png - Professional stress cloud (with color variation)") 
                print("  • safety_factor_cloud_3d.png - Safety factor analysis")
                print("  • deformed_shape_3d.png - Deformed shape analysis")
                print("  • wireframe_mesh_3d.png - Pure wireframe mesh model")
            else:
                print("  • professional_analysis_3d.png - Complete analysis overview")
            print("  • sensor_displacement.xdmf - ParaView displacement data")
            print("  • sensor_stress.xdmf - ParaView stress data")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """Main function for realistic sensor analysis"""
    # Create realistic 8mm diameter sensor with higher force for better stress visualization
    sensor = RealisticSensor3D(
        diameter=8e-3,      # 8mm diameter as requested
        force_magnitude=20.0  # Higher force for better stress gradients
    )
    
    # Run complete analysis with professional visualization
    sensor.run_complete_analysis()

if __name__ == "__main__":
    main()