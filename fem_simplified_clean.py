#!/usr/bin/env python3
"""
3D Multi-layer Flexible Sensor FEM Analysis using DOLFINx

Enhanced version with interactive 3D visualizations using PyVista/dolfinx.plot
Supports multi-layer structure with different material properties.
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')

# DOLFINx imports
try:
    import dolfinx
    from dolfinx import mesh, fem, io, default_scalar_type, plot
    from dolfinx.fem.petsc import LinearProblem
    import ufl
    from mpi4py import MPI
    from petsc4py import PETSc
    import gmsh
    HAS_DOLFINX = True
except ImportError:
    HAS_DOLFINX = False
    print("DOLFINx not available!")

# Visualization imports
try:
    import pyvista as pv
    HAS_PYVISTA = True
    print("Using PyVista for 3D visualization")
except ImportError:
    HAS_PYVISTA = False
    print("PyVista not available, using matplotlib for visualization")
    import matplotlib.pyplot as plt

class FlexibleSensor3DFEM:
    def __init__(self, radius=0.025, thickness_layers=None, E_layers=None, nu_layers=None, force_magnitude=10.0):
        """
        3D Multi-layer Flexible Sensor FEM Analysis using DOLFINx
        
        Parameters:
        radius: Sensor radius (m)
        thickness_layers: List of layer thicknesses [PCB, PVDF, Electrode, Contact] (m)
        E_layers: List of Young's moduli for each layer (Pa)
        nu_layers: List of Poisson's ratios for each layer
        force_magnitude: Applied force (N)
        """
        if not HAS_DOLFINX:
            raise ImportError("DOLFINx is required for this analysis")
            
        self.radius = radius
        self.force_magnitude = force_magnitude
        
        # Default layer properties if not specified
        if thickness_layers is None:
            self.thickness_layers = [25e-6, 35e-6, 5e-6, 10e-6]  # PCB, PVDF, Electrode, Contact
        else:
            self.thickness_layers = thickness_layers
            
        if E_layers is None:
            self.E_layers = [2.8e9, 2.0e9, 83e9, 200e9]  # PI, PVDF, Silver, Nickel
        else:
            self.E_layers = E_layers
            
        if nu_layers is None:
            self.nu_layers = [0.35, 0.39, 0.37, 0.31]  # PI, PVDF, Silver, Nickel
        else:
            self.nu_layers = nu_layers
        
        self.total_thickness = sum(self.thickness_layers)
        
        # Calculate equivalent properties
        self.calculate_equivalent_properties()
        
        # MPI communicator
        self.comm = MPI.COMM_WORLD
        
        # DOLFINx objects
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        self.facet_markers = None
        self.bcs = None
        self.f = None
        
        print(f"=== 3D Multi-layer Flexible Sensor FEM Analysis ===")
        print(f"Sensor radius: {self.radius*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Applied force: {self.force_magnitude} N")
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties using rule of mixtures"""
        # Volume fractions
        volume_fractions = [t/self.total_thickness for t in self.thickness_layers]
        
        # Equivalent Young's modulus (Voigt model)
        self.E_eq = sum(E * vf for E, vf in zip(self.E_layers, volume_fractions))
        
        # Equivalent Poisson's ratio
        self.nu_eq = sum(nu * vf for nu, vf in zip(self.nu_layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        print(f"Equivalent Material Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.2f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
    
    def create_3d_mesh(self, mesh_resolution=20):
        """Create 3D cylindrical mesh using Gmsh"""
        try:
            # Initialize Gmsh
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("sensor_3d")
            
            mesh_size = self.radius / mesh_resolution
            
            # Create bottom circle (z=0)
            center_bottom = gmsh.model.geo.addPoint(0, 0, 0, mesh_size)
            p1_bottom = gmsh.model.geo.addPoint(self.radius, 0, 0, mesh_size)
            p2_bottom = gmsh.model.geo.addPoint(0, self.radius, 0, mesh_size)
            p3_bottom = gmsh.model.geo.addPoint(-self.radius, 0, 0, mesh_size)
            p4_bottom = gmsh.model.geo.addPoint(0, -self.radius, 0, mesh_size)
            
            # Create arcs for bottom circle
            arc1_bottom = gmsh.model.geo.addCircleArc(p1_bottom, center_bottom, p2_bottom)
            arc2_bottom = gmsh.model.geo.addCircleArc(p2_bottom, center_bottom, p3_bottom)
            arc3_bottom = gmsh.model.geo.addCircleArc(p3_bottom, center_bottom, p4_bottom)
            arc4_bottom = gmsh.model.geo.addCircleArc(p4_bottom, center_bottom, p1_bottom)
            
            circle_loop_bottom = gmsh.model.geo.addCurveLoop([arc1_bottom, arc2_bottom, arc3_bottom, arc4_bottom])
            circle_surface_bottom = gmsh.model.geo.addPlaneSurface([circle_loop_bottom])
            
            # Create top circle (z=total_thickness)
            center_top = gmsh.model.geo.addPoint(0, 0, self.total_thickness, mesh_size)
            p1_top = gmsh.model.geo.addPoint(self.radius, 0, self.total_thickness, mesh_size)
            p2_top = gmsh.model.geo.addPoint(0, self.radius, self.total_thickness, mesh_size)
            p3_top = gmsh.model.geo.addPoint(-self.radius, 0, self.total_thickness, mesh_size)
            p4_top = gmsh.model.geo.addPoint(0, -self.radius, self.total_thickness, mesh_size)
            
            arc1_top = gmsh.model.geo.addCircleArc(p1_top, center_top, p2_top)
            arc2_top = gmsh.model.geo.addCircleArc(p2_top, center_top, p3_top)
            arc3_top = gmsh.model.geo.addCircleArc(p3_top, center_top, p4_top)
            arc4_top = gmsh.model.geo.addCircleArc(p4_top, center_top, p1_top)
            
            circle_loop_top = gmsh.model.geo.addCurveLoop([arc1_top, arc2_top, arc3_top, arc4_top])
            circle_surface_top = gmsh.model.geo.addPlaneSurface([circle_loop_top])
            
            # Create side surfaces
            line1 = gmsh.model.geo.addLine(p1_bottom, p1_top)
            line2 = gmsh.model.geo.addLine(p2_bottom, p2_top)
            line3 = gmsh.model.geo.addLine(p3_bottom, p3_top)
            line4 = gmsh.model.geo.addLine(p4_bottom, p4_top)
            
            side_loop1 = gmsh.model.geo.addCurveLoop([arc1_bottom, line2, -arc1_top, -line1])
            side_loop2 = gmsh.model.geo.addCurveLoop([arc2_bottom, line3, -arc2_top, -line2])
            side_loop3 = gmsh.model.geo.addCurveLoop([arc3_bottom, line4, -arc3_top, -line3])
            side_loop4 = gmsh.model.geo.addCurveLoop([arc4_bottom, line1, -arc4_top, -line4])
            
            side_surface1 = gmsh.model.geo.addPlaneSurface([side_loop1])
            side_surface2 = gmsh.model.geo.addPlaneSurface([side_loop2])
            side_surface3 = gmsh.model.geo.addPlaneSurface([side_loop3])
            side_surface4 = gmsh.model.geo.addPlaneSurface([side_loop4])
            
            # Create volume
            surface_loop = gmsh.model.geo.addSurfaceLoop([
                circle_surface_bottom, circle_surface_top,
                side_surface1, side_surface2, side_surface3, side_surface4
            ])
            volume = gmsh.model.geo.addVolume([surface_loop])
            
            # Synchronize and add physical groups
            gmsh.model.geo.synchronize()
            
            gmsh.model.addPhysicalGroup(3, [volume], 1)
            gmsh.model.setPhysicalName(3, 1, "sensor_volume")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_bottom], 2)
            gmsh.model.setPhysicalName(2, 2, "bottom_surface")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_top], 3)
            gmsh.model.setPhysicalName(2, 3, "top_surface")
            
            # Generate 3D mesh
            gmsh.model.mesh.generate(3)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, cell_markers, self.facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=3
            )
            
            gmsh.finalize()
            
            # Create vector function space (3D displacement field)
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (3,)))
            
            print(f"3D Mesh Created:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(3).size_local}")
            
        except Exception as e:
            print(f"3D Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
            
    def define_boundary_conditions(self):
        """Define boundary conditions: fixed bottom, load on top center"""
        # Fixed bottom surface (z=0)
        def bottom_surface(x):
            return np.isclose(x[2], 0.0, atol=1e-6)
        
        # Find DOFs on bottom surface
        bottom_facets = mesh.locate_entities_boundary(self.domain, 2, bottom_surface)
        bottom_dofs = fem.locate_dofs_topological(self.V, 2, bottom_facets)
        
        # Create zero displacement boundary condition
        zero_displacement = np.zeros(3, dtype=default_scalar_type)
        bc = fem.dirichletbc(zero_displacement, bottom_dofs, self.V)
        
        self.bcs = [bc]
        
        print(f"Boundary Conditions:")
        print(f"  Fixed bottom surface facets: {len(bottom_facets)}")
        print(f"  Fixed DOFs: {len(bottom_dofs)}")
        
    def define_load(self):
        """Define applied load on top center"""
        x = ufl.SpatialCoordinate(self.domain)
        
        # Point load at center of top surface
        load_radius = self.radius * 0.1  # Load applied over small central area
        load_intensity = self.force_magnitude / (np.pi * load_radius**2)
        
        # Load condition: on top surface and within load radius
        f_expr = ufl.conditional(
            ufl.And(
                ufl.le((x[0]**2 + x[1]**2), load_radius**2),
                ufl.ge(x[2], self.total_thickness * 0.9)
            ),
            ufl.as_vector([0, 0, -load_intensity]),
            ufl.as_vector([0, 0, 0])
        )
        
        self.f = f_expr
        print(f"Load applied: {self.force_magnitude} N over radius {load_radius*1000:.2f} mm")
    
    def solve_elasticity(self):
        """Solve 3D elasticity problem using DOLFINx"""
        print("Solving 3D elasticity equations...")
        
        # Define trial and test functions
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        # Define stress tensor
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        def sigma(u):
            return self.lambda_eq * ufl.nabla_div(u) * ufl.Identity(3) + 2*self.mu_eq*epsilon(u)
        
        # Variational problem
        a = ufl.inner(sigma(u), epsilon(v)) * ufl.dx
        L = ufl.dot(self.f, v) * ufl.dx
        
        # Solve linear problem
        problem = LinearProblem(a, L, self.bcs, 
                               petsc_options={"ksp_type": "preonly", "pc_type": "lu"})
        self.u = problem.solve()
        
        # Calculate maximum displacement
        u_array = self.u.x.array.reshape((-1, 3))
        max_displacement = np.max(np.linalg.norm(u_array, axis=1))
        print(f"Solution completed! Max displacement: {max_displacement*1e6:.3f} μm")
        
    def calculate_stress(self):
        """Calculate stress field"""
        print("Calculating stress field...")
        
        # Create tensor function space for stress
        S = fem.functionspace(self.domain, ("DG", 0, (3, 3)))
        
        # Define stress expression
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        def sigma(u):
            return self.lambda_eq * ufl.nabla_div(u) * ufl.Identity(3) + 2*self.mu_eq*epsilon(u)
        
        # Project stress onto function space
        stress_expr = sigma(self.u)
        self.stress = fem.Function(S)
        stress_projection = fem.Expression(stress_expr, S.element.interpolation_points())
        self.stress.interpolate(stress_projection)
        
        # Calculate von Mises stress
        stress_array = self.stress.x.array.reshape((-1, 9))
        von_mises_stress = []
        
        for i in range(stress_array.shape[0]):
            s = stress_array[i].reshape((3, 3))
            s11, s22, s33 = s[0,0], s[1,1], s[2,2]
            s12, s13, s23 = s[0,1], s[0,2], s[1,2]
            
            von_mises = np.sqrt(0.5 * ((s11-s22)**2 + (s22-s33)**2 + (s33-s11)**2 + 
                                      6*(s12**2 + s13**2 + s23**2)))
            von_mises_stress.append(von_mises)
        
        self.von_mises_stress = np.array(von_mises_stress)
        max_stress = np.max(self.von_mises_stress)
        print(f"Max von Mises stress: {max_stress/1e6:.2f} MPa")
        
    def visualize_3d_mesh(self):
        """Create interactive 3D mesh visualization"""
        print("Creating 3D mesh visualization...")
        
        if HAS_PYVISTA:
            try:
                # Convert DOLFINx mesh to PyVista
                topology, cell_types, geometry = plot.vtk_mesh(self.domain, 3)
                grid = pv.UnstructuredGrid(topology, cell_types, geometry)
                
                # Create plotter
                plotter = pv.Plotter()
                plotter.add_mesh(grid, show_edges=True, color='lightblue', opacity=0.8)
                plotter.add_title('3D Sensor Mesh', font_size=16)
                plotter.show_axes()
                plotter.view_isometric()
                
                # Save as HTML for interactivity
                plotter.export_html('/root/FEM/mesh_3d.html')
                plotter.close()
                
                print("Interactive 3D mesh saved as mesh_3d.html")
                
            except Exception as e:
                print(f"PyVista visualization failed: {e}")
                self._fallback_mesh_plot()
        else:
            self._fallback_mesh_plot()
    
    def _fallback_mesh_plot(self):
        """Fallback mesh visualization using matplotlib"""
        coordinates = self.domain.geometry.x
        
        fig = plt.figure(figsize=(12, 8))
        
        # XY projection (top view)
        ax1 = fig.add_subplot(221)
        ax1.scatter(coordinates[:, 0], coordinates[:, 1], c=coordinates[:, 2], 
                   cmap='viridis', s=2, alpha=0.7)
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_title('Top View (XY)')
        ax1.axis('equal')
        
        # XZ projection (side view)
        ax2 = fig.add_subplot(222)
        ax2.scatter(coordinates[:, 0], coordinates[:, 2], c=coordinates[:, 1], 
                   cmap='plasma', s=2, alpha=0.7)
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Z (m)')
        ax2.set_title('Side View (XZ)')
        
        # YZ projection (front view)
        ax3 = fig.add_subplot(223)
        ax3.scatter(coordinates[:, 1], coordinates[:, 2], c=coordinates[:, 0], 
                   cmap='coolwarm', s=2, alpha=0.7)
        ax3.set_xlabel('Y (m)')
        ax3.set_ylabel('Z (m)')
        ax3.set_title('Front View (YZ)')
        
        # 3D-like view
        ax4 = fig.add_subplot(224)
        # Create pseudo-3D effect
        x_proj = coordinates[:, 0] + 0.3 * coordinates[:, 2]
        y_proj = coordinates[:, 1] + 0.5 * coordinates[:, 2]
        ax4.scatter(x_proj, y_proj, c=coordinates[:, 2], cmap='viridis', s=2, alpha=0.7)
        ax4.set_xlabel('X projected')
        ax4.set_ylabel('Y projected')
        ax4.set_title('Pseudo-3D View')
        
        plt.suptitle('3D Sensor Mesh Visualization', fontsize=16)
        plt.tight_layout()
        plt.savefig('/root/FEM/mesh_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("3D mesh visualization saved as mesh_3d.png")
        
    def visualize_3d_displacement(self):
        """Create interactive 3D displacement visualization"""
        print("Creating 3D displacement visualization...")
        
        if HAS_PYVISTA:
            try:
                # Convert mesh to PyVista
                topology, cell_types, geometry = plot.vtk_mesh(self.domain, 3)
                grid = pv.UnstructuredGrid(topology, cell_types, geometry)
                
                # Get displacement data
                u_array = self.u.x.array.reshape((-1, 3))
                displacement_magnitude = np.linalg.norm(u_array, axis=1)
                
                # Add displacement magnitude as scalar field
                grid.point_data["Displacement_Magnitude"] = displacement_magnitude
                
                # Add displacement vectors
                grid.point_data["Displacement_Vector"] = u_array
                
                # Create plotter with displacement visualization
                plotter = pv.Plotter()
                
                # Add mesh with displacement coloring
                plotter.add_mesh(grid, scalars="Displacement_Magnitude", 
                               cmap='viridis', show_edges=False, opacity=0.9,
                               scalar_bar_args={'title': 'Displacement (m)'})
                
                # Add displacement vectors (scaled for visibility)
                scale_factor = 1000  # Scale up for visualization
                arrows = grid.glyph(orient="Displacement_Vector", scale="Displacement_Magnitude",
                                   factor=scale_factor, geom=pv.Arrow())
                plotter.add_mesh(arrows, color='red', opacity=0.7)
                
                plotter.add_title('3D Displacement Distribution', font_size=16)
                plotter.show_axes()
                plotter.view_isometric()
                
                # Save interactive HTML
                plotter.export_html('/root/FEM/displacement_3d.html')
                plotter.close()
                
                print("Interactive 3D displacement saved as displacement_3d.html")
                
            except Exception as e:
                print(f"PyVista displacement visualization failed: {e}")
                self._fallback_displacement_plot()
        else:
            self._fallback_displacement_plot()
    
    def _fallback_displacement_plot(self):
        """Fallback displacement visualization"""
        coordinates = self.domain.geometry.x
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        
        fig = plt.figure(figsize=(15, 10))
        
        # Top view with displacement
        ax1 = fig.add_subplot(221)
        scatter1 = ax1.scatter(coordinates[:, 0], coordinates[:, 1], 
                              c=displacement_magnitude, cmap='viridis', s=3)
        plt.colorbar(scatter1, ax=ax1, label='Displacement (m)')
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_title('Displacement Magnitude - Top View')
        ax1.axis('equal')
        
        # Side view with displacement
        ax2 = fig.add_subplot(222)
        scatter2 = ax2.scatter(coordinates[:, 0], coordinates[:, 2], 
                              c=displacement_magnitude, cmap='viridis', s=3)
        plt.colorbar(scatter2, ax=ax2, label='Displacement (m)')
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Z (m)')
        ax2.set_title('Displacement Magnitude - Side View')
        
        # Z-displacement component
        ax3 = fig.add_subplot(223)
        scatter3 = ax3.scatter(coordinates[:, 0], coordinates[:, 1], 
                              c=u_array[:, 2], cmap='RdBu_r', s=3)
        plt.colorbar(scatter3, ax=ax3, label='Z-Displacement (m)')
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Y (m)')
        ax3.set_title('Z-Displacement Component')
        ax3.axis('equal')
        
        # Summary
        ax4 = fig.add_subplot(224)
        ax4.axis('off')
        max_disp = np.max(displacement_magnitude)
        summary_text = f"""
Displacement Analysis Summary:

• Maximum displacement: {max_disp*1e6:.3f} μm
• Average displacement: {np.mean(displacement_magnitude)*1e6:.3f} μm
• Std displacement: {np.std(displacement_magnitude)*1e6:.3f} μm

• Max X-displacement: {np.max(np.abs(u_array[:, 0]))*1e6:.3f} μm
• Max Y-displacement: {np.max(np.abs(u_array[:, 1]))*1e6:.3f} μm
• Max Z-displacement: {np.max(np.abs(u_array[:, 2]))*1e6:.3f} μm

• Total nodes: {len(coordinates)}
• Applied force: {self.force_magnitude} N
        """
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, 
                fontsize=11, verticalalignment='top', fontfamily='monospace')
        
        plt.suptitle('3D Displacement Field Analysis', fontsize=16)
        plt.tight_layout()
        plt.savefig('/root/FEM/displacement_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("3D displacement visualization saved as displacement_3d.png")
        
    def visualize_3d_stress(self):
        """Create interactive 3D stress visualization"""
        print("Creating 3D stress visualization...")
        
        if HAS_PYVISTA:
            try:
                # Convert mesh to PyVista
                topology, cell_types, geometry = plot.vtk_mesh(self.domain, 3)
                grid = pv.UnstructuredGrid(topology, cell_types, geometry)
                
                # Add von Mises stress to mesh
                # Note: PyVista expects point data, but we have cell data
                # We'll interpolate cell data to points
                cell_centers = grid.cell_centers()
                
                # Create stress field on grid points by interpolation
                stress_points = np.zeros(grid.n_points)
                for i in range(grid.n_points):
                    # Find nearest cell center
                    distances = np.linalg.norm(cell_centers.points - grid.points[i], axis=1)
                    nearest_cell = np.argmin(distances)
                    if nearest_cell < len(self.von_mises_stress):
                        stress_points[i] = self.von_mises_stress[nearest_cell]
                
                grid.point_data["von_Mises_Stress"] = stress_points
                
                # Create plotter
                plotter = pv.Plotter()
                
                # Add mesh with stress coloring
                plotter.add_mesh(grid, scalars="von_Mises_Stress", 
                               cmap='plasma', show_edges=False, opacity=0.9,
                               scalar_bar_args={'title': 'von Mises Stress (Pa)'})
                
                plotter.add_title('3D Stress Distribution', font_size=16)
                plotter.show_axes()
                plotter.view_isometric()
                
                # Save interactive HTML
                plotter.export_html('/root/FEM/stress_3d.html')
                plotter.close()
                
                print("Interactive 3D stress saved as stress_3d.html")
                
            except Exception as e:
                print(f"PyVista stress visualization failed: {e}")
                self._fallback_stress_plot()
        else:
            self._fallback_stress_plot()
    
    def _fallback_stress_plot(self):
        """Fallback stress visualization"""
        coordinates = self.domain.geometry.x
        
        # Get cell centers for stress data
        cells = self.domain.topology.connectivity(3, 0)
        cell_centers = []
        
        # Handle DOLFINx connectivity format
        num_cells = self.domain.topology.index_map(3).size_local
        for i in range(num_cells):
            cell_nodes = cells.links(i)
            if len(cell_nodes) > 0:
                center = np.mean(coordinates[cell_nodes], axis=0)
                cell_centers.append(center)
        
        cell_centers = np.array(cell_centers) if cell_centers else np.array([]).reshape(0, 3)
        
        fig = plt.figure(figsize=(15, 10))
        
        # Top view with stress
        ax1 = fig.add_subplot(221)
        if len(cell_centers) > 0 and len(self.von_mises_stress) > 0:
            min_len = min(len(cell_centers), len(self.von_mises_stress))
            scatter1 = ax1.scatter(cell_centers[:min_len, 0], cell_centers[:min_len, 1], 
                                  c=self.von_mises_stress[:min_len], cmap='plasma', s=10)
            plt.colorbar(scatter1, ax=ax1, label='von Mises Stress (Pa)')
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_title('Stress Distribution - Top View')
        ax1.axis('equal')
        
        # Side view with stress
        ax2 = fig.add_subplot(222)
        if len(cell_centers) > 0 and len(self.von_mises_stress) > 0:
            scatter2 = ax2.scatter(cell_centers[:min_len, 0], cell_centers[:min_len, 2], 
                                  c=self.von_mises_stress[:min_len], cmap='plasma', s=10)
            plt.colorbar(scatter2, ax=ax2, label='von Mises Stress (Pa)')
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Z (m)')
        ax2.set_title('Stress Distribution - Side View')
        
        # Stress histogram
        ax3 = fig.add_subplot(223)
        if len(self.von_mises_stress) > 0:
            ax3.hist(self.von_mises_stress/1e6, bins=50, alpha=0.7, color='red')
        ax3.set_xlabel('von Mises Stress (MPa)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Stress Distribution Histogram')
        ax3.grid(True, alpha=0.3)
        
        # Summary
        ax4 = fig.add_subplot(224)
        ax4.axis('off')
        if len(self.von_mises_stress) > 0:
            max_stress = np.max(self.von_mises_stress)
            avg_stress = np.mean(self.von_mises_stress)
            summary_text = f"""
Stress Analysis Summary:

• Maximum von Mises stress: {max_stress/1e6:.2f} MPa
• Average stress: {avg_stress/1e6:.2f} MPa
• Std stress: {np.std(self.von_mises_stress)/1e6:.2f} MPa

• Applied force: {self.force_magnitude} N
• Total elements: {len(self.von_mises_stress)}

Material Properties:
• Equivalent E: {self.E_eq/1e9:.2f} GPa
• Equivalent ν: {self.nu_eq:.3f}
            """
        else:
            summary_text = "No stress data available"
        
        ax4.text(0.1, 0.9, summary_text, transform=ax4.transAxes, 
                fontsize=11, verticalalignment='top', fontfamily='monospace')
        
        plt.suptitle('3D Stress Field Analysis', fontsize=16)
        plt.tight_layout()
        plt.savefig('/root/FEM/stress_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("3D stress visualization saved as stress_3d.png")
        
    def export_results(self):
        """Export results to XDMF format"""
        if self.domain is None:
            return
            
        # Export displacement
        with io.XDMFFile(self.domain.comm, "/root/FEM/displacement_3d.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        # Export stress
        with io.XDMFFile(self.domain.comm, "/root/FEM/stress_3d.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("Results exported to XDMF format")
    
    def run_analysis(self):
        """Run complete 3D FEM analysis"""
        try:
            print("\n1. Creating 3D mesh...")
            self.create_3d_mesh()
            
            print("\n2. Defining boundary conditions...")
            self.define_boundary_conditions()
            
            print("\n3. Defining load...")
            self.define_load()
            
            print("\n4. Solving elasticity equations...")
            self.solve_elasticity()
            
            print("\n5. Calculating stress field...")
            self.calculate_stress()
            
            print("\n6. Creating visualizations...")
            self.visualize_3d_mesh()
            self.visualize_3d_displacement()
            self.visualize_3d_stress()
            
            print("\n7. Exporting results...")
            self.export_results()
            
            print("\n=== Analysis completed successfully! ===")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """
    Main function: Run 3D multi-layer flexible sensor FEM analysis
    """
    # Create 3D sensor analysis instance
    sensor = FlexibleSensor3DFEM(
        radius=0.025,                                    # Sensor radius (m)
        thickness_layers=[25e-6, 35e-6, 5e-6, 10e-6],  # Layer thicknesses (m)
        E_layers=[2.8e9, 2.0e9, 83e9, 200e9],          # Young's moduli (Pa)
        nu_layers=[0.35, 0.39, 0.37, 0.31],            # Poisson's ratios
        force_magnitude=10.0                             # Applied force (N)
    )
    
    # Run complete analysis
    sensor.run_analysis()
    
    print("\nAnalysis complete! Results saved in /root/FEM/")
    if HAS_PYVISTA:
        print("Interactive 3D visualizations:")
        print("  - mesh_3d.html (3D mesh)")
        print("  - displacement_3d.html (3D displacement)")
        print("  - stress_3d.html (3D stress)")
    else:
        print("Static visualizations:")
        print("  - mesh_3d.png")
        print("  - displacement_3d.png")
        print("  - stress_3d.png")

if __name__ == "__main__":
    main()