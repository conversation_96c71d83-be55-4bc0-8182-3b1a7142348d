#!/usr/bin/env python3
"""
Realistic 3D Multi-layer Flexible Sensor with Cloud Visualization
Creates professional 3D contour plots similar to commercial FEM software
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')

# DOLFINx imports
try:
    import dolfinx
    from dolfinx import mesh, fem, io, default_scalar_type, plot
    from dolfinx.fem.petsc import LinearProblem
    import ufl
    from mpi4py import MPI
    from petsc4py import PETSc
    import gmsh
    HAS_DOLFINX = True
except ImportError:
    HAS_DOLFINX = False
    print("DOLFINx not available!")

# Try to install and import pyvista for better visualization
try:
    import pyvista as pv
    HAS_PYVISTA = True
    print("Using PyVista for professional 3D visualization")
    # Configure PyVista for better rendering
    pv.set_plot_theme("document")
    pv.global_theme.background = 'white'
    pv.global_theme.window_size = [1200, 800]
except ImportError:
    HAS_PYVISTA = False
    print("Installing PyVista for better visualization...")
    import subprocess
    import sys
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyvista", "--timeout", "60"])
        import pyvista as pv
        HAS_PYVISTA = True
        pv.set_plot_theme("document")
        pv.global_theme.background = 'white'
    except:
        HAS_PYVISTA = False
        import matplotlib.pyplot as plt

class RealisticSensor3D:
    def __init__(self, diameter=8e-3, force_magnitude=10.0):
        """
        Realistic 3D Multi-layer Flexible Sensor (8mm diameter)
        
        Parameters:
        diameter: Sensor diameter (m) - default 8mm
        force_magnitude: Applied force (N)
        """
        if not HAS_DOLFINX:
            raise ImportError("DOLFINx is required for this analysis")
            
        self.radius = diameter / 2.0  # 4mm radius
        self.force_magnitude = force_magnitude
        
        # Realistic layer thicknesses for 8mm sensor
        self.thickness_pcb = 50e-6      # PI substrate: 50μm (thicker for 8mm sensor)
        self.thickness_pvdf = 40e-6     # PVDF film: 40μm
        self.thickness_electrode = 8e-6  # Electrode: 8μm
        self.thickness_contact = 15e-6   # Contact: 15μm
        self.total_thickness = (self.thickness_pcb + self.thickness_pvdf + 
                               self.thickness_electrode + self.thickness_contact)
        
        # Enhanced material properties for flexible sensor with fatigue data
        self.material_props = {
            'PCB': {
                'E': 4e6, 'nu': 0.38, 'color': '#1f77b4',      # Flexible PI substrate ~4 MPa
                'yield_strength': 6e6,    # Yield strength (Pa)
                'fatigue_strength': 2e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.1,  # Fatigue exponent (b)
                'ultimate_strength': 8e6,  # Ultimate tensile strength (Pa)
                'endurance_limit': 1e6,    # Endurance limit (Pa)
                'layer_id': 1,            # Layer identifier
                'name': 'PCB基材'
            },
            'PVDF': {
                'E': 3e9, 'nu': 0.35, 'color': '#ff7f0e',     # PVDF ~3 GPa (keeps piezo property)
                'yield_strength': 60e6,   # Yield strength (Pa)
                'fatigue_strength': 50e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.08, # Fatigue exponent (b)
                'ultimate_strength': 80e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 20e6,   # Endurance limit (Pa)
                'layer_id': 2,            # Layer identifier
                'name': 'PVDF薄膜'
            },
            'Electrode': {
                'E': 200e6, 'nu': 0.40, 'color': '#2ca02c', # Flexible conductor ~200 MPa
                'yield_strength': 45e6,   # Yield strength (Pa)
                'fatigue_strength': 30e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.12, # Fatigue exponent (b)
                'ultimate_strength': 60e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 10e6,   # Endurance limit (Pa)
                'layer_id': 3,            # Layer identifier
                'name': '电极层'
            },
            'Contact': {
                'E': 100e6, 'nu': 0.42, 'color': '#d62728',  # Soft contact material ~100 MPa
                'yield_strength': 25e6,   # Yield strength (Pa)
                'fatigue_strength': 15e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.15, # Fatigue exponent (b)
                'ultimate_strength': 30e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 5e6,    # Endurance limit (Pa)
                'layer_id': 4,            # Layer identifier
                'name': '接触层'
            }
        }

        # 层厚度累积计算（用于确定材料层位置）
        self.layer_z_bounds = {
            'PCB': (0, self.thickness_pcb),
            'PVDF': (self.thickness_pcb, self.thickness_pcb + self.thickness_pvdf),
            'Electrode': (self.thickness_pcb + self.thickness_pvdf,
                         self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode),
            'Contact': (self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode,
                       self.total_thickness)
        }
        
        # Calculate equivalent properties
        self.calculate_equivalent_properties()
        
        # DOLFINx objects
        self.comm = MPI.COMM_WORLD
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        self.von_mises_stress = None
        
        print(f"=== Realistic 3D Sensor Analysis (Professional Grade) ===")
        print(f"Sensor diameter: {self.radius*2*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Applied force: {self.force_magnitude} N")
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties"""
        layers = [
            (self.material_props['PCB'], self.thickness_pcb),
            (self.material_props['PVDF'], self.thickness_pvdf),
            (self.material_props['Electrode'], self.thickness_electrode),
            (self.material_props['Contact'], self.thickness_contact)
        ]
        
        # Volume fractions
        volume_fractions = [t/self.total_thickness for _, t in layers]
        
        # Equivalent properties using rule of mixtures
        self.E_eq = sum(props['E'] * vf for (props, _), vf in zip(layers, volume_fractions))
        self.nu_eq = sum(props['nu'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        print(f"Equivalent Material Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.2f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
    
    def create_3d_mesh(self, mesh_resolution=25):
        """Create high-quality 3D mesh with material layer separation"""
        try:
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("realistic_sensor")

            # Finer mesh for better visualization
            mesh_size = self.radius / mesh_resolution

            # Create points for each layer interface
            n_points = 12  # More points for smoother geometry
            layer_heights = [0, self.thickness_pcb,
                           self.thickness_pcb + self.thickness_pvdf,
                           self.thickness_pcb + self.thickness_pvdf + self.thickness_electrode,
                           self.total_thickness]

            # Store all points and surfaces for each layer
            layer_points = []
            layer_surfaces = []

            # Create points and surfaces for each layer interface
            for layer_idx, z in enumerate(layer_heights):
                center = gmsh.model.geo.addPoint(0, 0, z, mesh_size)
                points = []

                for i in range(n_points):
                    angle = 2 * np.pi * i / n_points
                    x = self.radius * np.cos(angle)
                    y = self.radius * np.sin(angle)
                    point = gmsh.model.geo.addPoint(x, y, z, mesh_size)
                    points.append(point)

                # Create arcs for circle
                arcs = []
                for i in range(n_points):
                    next_i = (i + 1) % n_points
                    arc = gmsh.model.geo.addCircleArc(points[i], center, points[next_i])
                    arcs.append(arc)

                circle_loop = gmsh.model.geo.addCurveLoop(arcs)
                circle_surface = gmsh.model.geo.addPlaneSurface([circle_loop])

                layer_points.append((center, points, arcs))
                layer_surfaces.append(circle_surface)
            
            # Create volumes for each material layer
            layer_volumes = []
            material_names = ['PCB', 'PVDF', 'Electrode', 'Contact']

            for layer_idx in range(len(material_names)):
                bottom_surface = layer_surfaces[layer_idx]
                top_surface = layer_surfaces[layer_idx + 1]

                # Create side surfaces for this layer
                bottom_center, bottom_points, bottom_arcs = layer_points[layer_idx]
                top_center, top_points, top_arcs = layer_points[layer_idx + 1]

                side_surfaces = []
                for i in range(n_points):
                    next_i = (i + 1) % n_points
                    line_bottom = gmsh.model.geo.addLine(bottom_points[i], top_points[i])
                    line_top = gmsh.model.geo.addLine(bottom_points[next_i], top_points[next_i])

                    side_loop = gmsh.model.geo.addCurveLoop([
                        bottom_arcs[i], line_top, -layer_points[layer_idx + 1][2][i], -line_bottom
                    ])
                    side_surface = gmsh.model.geo.addPlaneSurface([side_loop])
                    side_surfaces.append(side_surface)

                # Create volume for this layer
                all_surfaces = [bottom_surface, top_surface] + side_surfaces
                surface_loop = gmsh.model.geo.addSurfaceLoop(all_surfaces)
                volume = gmsh.model.geo.addVolume([surface_loop])
                layer_volumes.append(volume)
            
            # Synchronize and add physical groups
            gmsh.model.geo.synchronize()

            # Add physical groups for each material layer
            for i, (volume, material_name) in enumerate(zip(layer_volumes, material_names)):
                layer_id = self.material_props[material_name]['layer_id']
                gmsh.model.addPhysicalGroup(3, [volume], layer_id)
                gmsh.model.setPhysicalName(3, layer_id, f"{material_name}_layer")

            # Add physical groups for boundary surfaces
            gmsh.model.addPhysicalGroup(2, [layer_surfaces[0]], 10)  # Bottom surface
            gmsh.model.setPhysicalName(2, 10, "bottom_surface")

            gmsh.model.addPhysicalGroup(2, [layer_surfaces[-1]], 11)  # Top surface
            gmsh.model.setPhysicalName(2, 11, "top_surface")

            # Store material layer information for later use
            self.material_layer_ids = {name: self.material_props[name]['layer_id']
                                     for name in material_names}
            
            # Generate high-quality mesh
            gmsh.option.setNumber("Mesh.Algorithm", 5)  # Delaunay
            gmsh.option.setNumber("Mesh.Algorithm3D", 1)  # Delaunay
            gmsh.option.setNumber("Mesh.Optimize", 1)
            
            gmsh.model.mesh.generate(3)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, self.cell_markers, self.facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=3
            )
            
            gmsh.finalize()
            
            # Create vector function space
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (3,)))
            
            print(f"High-Quality 3D Mesh Created:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(3).size_local}")
            
        except Exception as e:
            print(f"Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
    
    def solve_fem_problem(self):
        """Solve the complete FEM problem"""
        # Simple boundary conditions - fix bottom surface completely
        def bottom_surface(x):
            return np.isclose(x[2], 0.0, atol=1e-6)
        
        # Apply clamped BC at bottom
        bottom_facets = mesh.locate_entities_boundary(self.domain, 2, bottom_surface)
        bottom_dofs = fem.locate_dofs_topological(self.V, 2, bottom_facets)
        
        zero_displacement = np.zeros(3, dtype=default_scalar_type)
        bc = fem.dirichletbc(zero_displacement, bottom_dofs, self.V)
        self.bcs = [bc]
        
        print(f"Boundary condition debugging:")
        print(f"  Bottom surface DOFs constrained: {len(bottom_dofs)}")
        print(f"  Total DOFs in system: {self.V.dofmap.index_map.size_global}")
        
        # Apply uniform pressure on top surface
        def top_surface(x):
            return np.isclose(x[2], self.total_thickness, atol=1e-6)
        
        top_facets = mesh.locate_entities_boundary(self.domain, 2, top_surface)
        
        # Create function space for pressure
        Q = fem.functionspace(self.domain, ("Lagrange", 1))
        pressure = fem.Function(Q)
        pressure.x.array[:] = -self.force_magnitude / (np.pi * self.radius**2)  # Uniform pressure
        
        # Define the problem
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        # Define material-dependent constitutive relations
        def sigma(u, E, nu):
            lambda_param = (E * nu) / ((1 + nu) * (1 - 2*nu))
            mu_param = E / (2 * (1 + nu))
            return lambda_param * ufl.nabla_div(u) * ufl.Identity(3) + 2*mu_param*epsilon(u)

        # Create material property functions
        Q_scalar = fem.functionspace(self.domain, ("DG", 0))
        E_func = fem.Function(Q_scalar)
        nu_func = fem.Function(Q_scalar)

        # Assign material properties to each cell based on layer
        cell_map = self.domain.topology.index_map(3)
        num_cells = cell_map.size_local

        # Get cell markers to identify material layers
        if hasattr(self, 'cell_markers'):
            for cell_idx in range(num_cells):
                layer_id = self.cell_markers.values[cell_idx]
                for material_name, props in self.material_props.items():
                    if props['layer_id'] == layer_id:
                        E_func.x.array[cell_idx] = props['E']
                        nu_func.x.array[cell_idx] = props['nu']
                        break
        else:
            # Fallback: use equivalent properties
            E_func.x.array[:] = self.E_eq
            nu_func.x.array[:] = self.nu_eq

        # Weak form with material-dependent properties
        a = ufl.inner(sigma(u, E_func, nu_func), epsilon(v)) * ufl.dx
        
        # Apply pressure as surface load
        ds = ufl.Measure("ds", domain=self.domain, subdomain_data=self.facet_markers)
        L = pressure * ufl.dot(ufl.as_vector([0, 0, 1]), v) * ds(11)  # Top surface has marker 11
        
        print(f"Load debugging info:")
        print(f"  Uniform pressure: {pressure.x.array[0]:.2e} Pa")
        print(f"  Applied over full top surface")
        
        # Solve with more robust solver options
        problem = LinearProblem(a, L, self.bcs, 
                               petsc_options={"ksp_type": "cg", "pc_type": "gamg", "ksp_rtol": 1e-8})
        self.u = problem.solve()
        
        # Calculate stress with material-dependent properties
        self.calculate_multilayer_stress_analysis()
        
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        max_displacement = np.max(displacement_magnitude)
        max_stress = np.max(self.von_mises_stress)
        
        # More detailed displacement analysis
        max_u_x = np.max(np.abs(u_array[:, 0]))
        max_u_y = np.max(np.abs(u_array[:, 1]))
        max_u_z = np.max(np.abs(u_array[:, 2]))
        
        print(f"FEM Solution Completed:")
        print(f"  Max displacement magnitude: {max_displacement:.6e} m = {max_displacement*1e6:.6f} μm")
        print(f"  Max X-displacement: {max_u_x:.6e} m = {max_u_x*1e6:.6f} μm")
        print(f"  Max Y-displacement: {max_u_y:.6e} m = {max_u_y*1e6:.6f} μm")
        print(f"  Max Z-displacement: {max_u_z:.6e} m = {max_u_z*1e6:.6f} μm")
        print(f"  Max von Mises stress: {max_stress:.6e} Pa = {max_stress/1e6:.6f} MPa")
        print(f"  Non-zero displacements: {np.count_nonzero(displacement_magnitude)} out of {len(displacement_magnitude)}")

        # Perform safety assessment
        self.perform_safety_assessment()

    def calculate_multilayer_stress_analysis(self):
        """Calculate stress for each material layer separately"""
        print("\n=== 多层材料应力分析 ===")

        # Calculate stress tensor
        S = fem.functionspace(self.domain, ("DG", 0, (3, 3)))

        # Create stress functions for each material layer
        self.layer_stresses = {}
        self.layer_von_mises = {}

        # Define stress calculation functions
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)

        def sigma_material(u, E, nu):
            lambda_param = (E * nu) / ((1 + nu) * (1 - 2*nu))
            mu_param = E / (2 * (1 + nu))
            return lambda_param * ufl.nabla_div(u) * ufl.Identity(3) + 2*mu_param*epsilon(u)

        # Calculate stress using equivalent properties first (simplified approach)
        stress_expr = sigma_material(self.u, self.E_eq, self.nu_eq)
        stress_func = fem.Function(S)
        stress_projection = fem.Expression(stress_expr, S.element.interpolation_points())
        stress_func.interpolate(stress_projection)

        # Calculate von Mises stress for all cells
        stress_array = stress_func.x.array.reshape((-1, 9))
        all_von_mises = []

        for i in range(stress_array.shape[0]):
            s = stress_array[i].reshape((3, 3))
            s11, s22, s33 = s[0,0], s[1,1], s[2,2]
            s12, s13, s23 = s[0,1], s[0,2], s[1,2]

            von_mises = np.sqrt(0.5 * ((s11-s22)**2 + (s22-s33)**2 + (s33-s11)**2 +
                                      6*(s12**2 + s13**2 + s23**2)))
            all_von_mises.append(von_mises)

        all_von_mises = np.array(all_von_mises)

        # Separate stress by material layer based on cell position
        cell_centers = []
        if hasattr(self.domain, 'geometry'):
            # Get cell centers to determine material layer
            tdim = self.domain.topology.dim
            cell_map = self.domain.topology.index_map(tdim)
            num_cells = cell_map.size_local

            # Calculate cell centers
            for cell_idx in range(num_cells):
                # Get vertices of the cell
                cell_vertices = self.domain.topology.connectivity(tdim, 0).links(cell_idx)
                vertex_coords = self.domain.geometry.x[cell_vertices]
                cell_center = np.mean(vertex_coords, axis=0)
                cell_centers.append(cell_center)

        # Assign stress to material layers based on Z-coordinate
        for material_name, props in self.material_props.items():
            z_min, z_max = self.layer_z_bounds[material_name]
            layer_stresses = []

            for i, center in enumerate(cell_centers):
                if len(center) >= 3 and z_min <= center[2] <= z_max:
                    layer_stresses.append(all_von_mises[i])

            if layer_stresses:
                self.layer_von_mises[material_name] = np.array(layer_stresses)
                max_stress = np.max(layer_stresses)
            else:
                # Fallback: use overall stress scaled by material properties
                stress_scale = props['E'] / self.E_eq
                scaled_stress = all_von_mises * stress_scale
                self.layer_von_mises[material_name] = scaled_stress
                max_stress = np.max(scaled_stress)

            # Print layer stress summary
            print(f"{props['name']}:")
            print(f"  最大von Mises应力: {max_stress:.2e} Pa = {max_stress/1e6:.2f} MPa")
            print(f"  屈服强度: {props['yield_strength']/1e6:.1f} MPa")
            if max_stress > 0:
                print(f"  安全系数: {props['yield_strength']/max_stress:.2f}")
            else:
                print(f"  安全系数: ∞")

        # Calculate overall von Mises stress (for compatibility)
        self.von_mises_stress = all_von_mises

        # Store stress function for export
        self.stress = stress_func

    def perform_safety_assessment(self):
        """Perform comprehensive safety assessment for each material layer"""
        print("\n=== 安全性评估 ===")

        safety_factors = {}
        critical_materials = []

        for material_name, props in self.material_props.items():
            max_stress = np.max(self.layer_von_mises[material_name])
            yield_strength = props['yield_strength']
            ultimate_strength = props['ultimate_strength']

            # Calculate safety factors
            sf_yield = yield_strength / max_stress if max_stress > 0 else float('inf')
            sf_ultimate = ultimate_strength / max_stress if max_stress > 0 else float('inf')

            safety_factors[material_name] = {
                'yield_sf': sf_yield,
                'ultimate_sf': sf_ultimate,
                'max_stress': max_stress
            }

            # Check if material is critical (safety factor < 2.0)
            if sf_yield < 2.0:
                critical_materials.append(material_name)

            # Print detailed assessment
            print(f"\n{props['name']} 安全评估:")
            print(f"  最大应力: {max_stress/1e6:.2f} MPa")
            print(f"  屈服强度: {yield_strength/1e6:.1f} MPa")
            print(f"  极限强度: {ultimate_strength/1e6:.1f} MPa")
            print(f"  屈服安全系数: {sf_yield:.2f}")
            print(f"  极限安全系数: {sf_ultimate:.2f}")

            if sf_yield >= 2.0:
                print(f"  状态: ✓ 安全")
            elif sf_yield >= 1.0:
                print(f"  状态: ⚠ 警告 - 安全系数偏低")
            else:
                print(f"  状态: ✗ 危险 - 超过屈服强度")

        # Overall assessment
        min_safety_factor = min(sf['yield_sf'] for sf in safety_factors.values())
        print(f"\n整体评估:")
        print(f"  最小安全系数: {min_safety_factor:.2f}")

        if min_safety_factor >= 2.0:
            print(f"  整体状态: ✓ 设计安全")
        elif min_safety_factor >= 1.0:
            print(f"  整体状态: ⚠ 需要优化设计")
        else:
            print(f"  整体状态: ✗ 设计不安全，需要重新设计")

        if critical_materials:
            print(f"  关键材料: {', '.join([self.material_props[m]['name'] for m in critical_materials])}")

        # Store results for visualization
        self.safety_assessment = safety_factors

    def create_professional_cloud_visualization(self):
        """Create professional 3D cloud visualization like commercial FEM software"""
        print("Creating professional 3D cloud visualization...")
        
        if not HAS_PYVISTA:
            print("PyVista not available. Skipping visualization.")
            return
        
        try:
            # Convert mesh to PyVista
            topology, cell_types, geometry = plot.vtk_mesh(self.domain, 3)
            grid = pv.UnstructuredGrid(topology, cell_types, geometry)
            
            # Get displacement and stress data
            u_array = self.u.x.array.reshape((-1, 3))
            displacement_magnitude = np.linalg.norm(u_array, axis=1)
            
            # Add data to grid
            grid.point_data["Displacement_Magnitude"] = displacement_magnitude
            grid.point_data["Displacement_X"] = u_array[:, 0]
            grid.point_data["Displacement_Y"] = u_array[:, 1]
            grid.point_data["Displacement_Z"] = u_array[:, 2]
            
            # Interpolate stress data to points
            stress_points = np.zeros(grid.n_points)
            cell_centers = grid.cell_centers()
            
            for i in range(grid.n_points):
                distances = np.linalg.norm(cell_centers.points - grid.points[i], axis=1)
                nearest_cell = np.argmin(distances)
                if nearest_cell < len(self.von_mises_stress):
                    stress_points[i] = self.von_mises_stress[nearest_cell]
            
            grid.point_data["von_Mises_Stress"] = stress_points

            # Add safety factor data
            safety_factor_points = np.zeros(grid.n_points)
            for i in range(grid.n_points):
                distances = np.linalg.norm(cell_centers.points - grid.points[i], axis=1)
                nearest_cell = np.argmin(distances)
                if nearest_cell < len(self.von_mises_stress):
                    stress_val = self.von_mises_stress[nearest_cell]
                    if stress_val > 0:
                        # Use minimum safety factor from all materials
                        min_sf = float('inf')
                        for material_name, props in self.material_props.items():
                            sf = props['yield_strength'] / stress_val
                            min_sf = min(min_sf, sf)
                        safety_factor_points[i] = min_sf
                    else:
                        safety_factor_points[i] = 10.0  # High safety factor for zero stress

            grid.point_data["Safety_Factor"] = np.clip(safety_factor_points, 0, 10)  # Clip for visualization

            # Create multiple professional visualizations
            self._create_displacement_cloud(grid)
            self._create_stress_cloud(grid)
            self._create_safety_factor_cloud(grid)
            self._create_deformed_visualization(grid)
            self._create_wireframe_mesh_visualization(grid)
            
        except Exception as e:
            print(f"PyVista visualization failed: {e}")
            print("Skipping visualization due to missing dependencies.")
    
    def _create_displacement_cloud(self, grid):
        """Create displacement cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Add mesh with displacement coloring
        mesh_actor = plotter.add_mesh(
            grid, 
            scalars="Displacement_Magnitude",
            cmap='viridis',  # Professional colormap
            show_edges=True,
            edge_color='black',
            line_width=1.0,
            opacity=0.8,
            scalar_bar_args={
                'title': 'Displacement (m)',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1e',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )
        
        # Add wireframe overlay for better depth perception
        wireframe = grid.extract_surface()
        plotter.add_mesh(wireframe, style='wireframe', color='black', opacity=0.1, line_width=0.5)
        
        # Professional lighting and camera setup
        plotter.add_title('3D Displacement Field - Cloud Visualization', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        
        # Add lighting for professional appearance
        light = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        plotter.add_light(light)
        
        # Save high-resolution image with anti-aliasing
        plotter.screenshot('/root/FEM/displacement_cloud_3d.png', 
                          transparent_background=False, 
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Professional displacement cloud saved: displacement_cloud_3d.png")

    def _create_safety_factor_cloud(self, grid):
        """Create safety factor cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])

        # Add mesh with safety factor coloring
        mesh_actor = plotter.add_mesh(
            grid,
            scalars="Safety_Factor",
            cmap='RdYlGn',  # Red-Yellow-Green colormap (red=dangerous, green=safe)
            show_edges=True,
            edge_color='black',
            line_width=1.0,
            opacity=0.8,
            clim=[0.5, 5.0],  # Safety factor range
            scalar_bar_args={
                'title': 'Safety Factor',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1f',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )

        # Add wireframe overlay
        wireframe = grid.extract_surface()
        plotter.add_mesh(wireframe, style='wireframe', color='black', opacity=0.1, line_width=0.5)

        # Professional setup
        plotter.add_title('Safety Factor Analysis - Material Stress Assessment', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()

        # Add lighting
        light = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        plotter.add_light(light)

        # Save image
        plotter.screenshot('/root/FEM/safety_factor_cloud_3d.png',
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)
        plotter.close()

        print("Safety factor cloud saved: safety_factor_cloud_3d.png")

    def _create_stress_cloud(self, grid):
        """Create stress cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Add mesh with stress coloring using professional colormap
        mesh_actor = plotter.add_mesh(
            grid, 
            scalars="von_Mises_Stress",
            cmap='viridis',  # Professional colormap
            show_edges=True,
            edge_color='black',
            line_width=1.0,
            opacity=0.8,
            scalar_bar_args={
                'title': 'von Mises Stress (Pa)',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1e',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )
        
        # Add contour lines for better visualization
        contours = grid.contour(scalars="von_Mises_Stress", isosurfaces=10)
        plotter.add_mesh(contours, style='wireframe', color='black', opacity=0.3, line_width=1.0)
        
        # Professional setup
        plotter.add_title('3D Stress Distribution - Cloud Visualization', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        
        # Enhanced lighting
        light1 = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        light2 = pv.Light(position=(-1, -1, 1), focal_point=(0, 0, 0), color='white', intensity=0.5)
        plotter.add_light(light1)
        plotter.add_light(light2)
        
        plotter.screenshot('/root/FEM/stress_cloud_3d.png', 
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Professional stress cloud saved: stress_cloud_3d.png")
    
    def _create_deformed_visualization(self, grid):
        """Create deformed shape visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Scale displacement for visualization
        scale_factor = 1000  # Amplify for visibility
        u_array = self.u.x.array.reshape((-1, 3))
        
        # Create deformed mesh
        deformed_points = grid.points + scale_factor * u_array
        deformed_grid = grid.copy()
        deformed_grid.points = deformed_points
        
        # Show original mesh
        plotter.add_mesh(grid, color='lightgray', opacity=0.3, label='Original')
        
        # Show deformed mesh with displacement coloring
        plotter.add_mesh(
            deformed_grid,
            scalars="Displacement_Magnitude",
            cmap='viridis',
            show_edges=True,
            edge_color='black',
            line_width=1.5,
            opacity=0.8,
            label='Deformed',
            scalar_bar_args={
                'title': f'Displacement (m) - Scale: {scale_factor}x',
                'title_font_size': 28,
                'label_font_size': 24,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.1e',
                'font_family': 'arial',
                'width': 0.08,
                'height': 0.7,
                'position_x': 0.88,
                'position_y': 0.15,
                'vertical': True,
                'shadow': True
            }
        )
        
        plotter.add_title('Deformed Shape Analysis (Amplified)', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        plotter.add_legend()
        
        plotter.screenshot('/root/FEM/deformed_shape_3d.png', 
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Deformed shape visualization saved: deformed_shape_3d.png")
    
    def _create_wireframe_mesh_visualization(self, grid):
        """Create pure wireframe mesh visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[2400, 1800])  # Higher resolution
        
        # Extract surface mesh for better wireframe visualization
        surface_mesh = grid.extract_surface()
        
        # Add pure wireframe mesh
        plotter.add_mesh(
            surface_mesh,
            style='wireframe',
            color='black',
            line_width=2.0,
            opacity=1.0,
            label='Surface Mesh'
        )
        
        # Add mesh edges with different color for better visibility
        plotter.add_mesh(
            grid,
            style='wireframe',
            color='darkblue',
            line_width=1.0,
            opacity=0.6,
            label='Volume Mesh'
        )
        
        # Add some key mesh points for reference
        plotter.add_mesh(
            grid.extract_surface(),
            style='points',
            color='red',
            point_size=8.0,
            opacity=0.8,
            label='Mesh Nodes'
        )
        
        # Clean setup - no titles, axes, or legends
        plotter.view_isometric()
        
        # Enhanced lighting for wireframe visibility
        light1 = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white', intensity=1.0)
        light2 = pv.Light(position=(-1, -1, 1), focal_point=(0, 0, 0), color='white', intensity=0.7)
        plotter.add_light(light1)
        plotter.add_light(light2)
        
        # Save high-resolution wireframe image
        plotter.screenshot('/root/FEM/wireframe_mesh_3d.png', 
                          transparent_background=False,
                          window_size=[2400, 1800],
                          scale=2)  # Super-sampling for better quality
        plotter.close()
        
        print("Pure wireframe mesh visualization saved: wireframe_mesh_3d.png")
    
    def _create_matplotlib_fallback(self):
        """Fallback visualization using matplotlib"""
        print("Using matplotlib fallback visualization...")
        import matplotlib.pyplot as plt
        
        coordinates = self.domain.geometry.x
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        
        # Create professional-looking plots
        fig = plt.figure(figsize=(16, 12))
        
        # Displacement cloud
        ax1 = fig.add_subplot(221, projection='3d')
        scatter = ax1.scatter(coordinates[:, 0], coordinates[:, 1], coordinates[:, 2],
                            c=displacement_magnitude, cmap='rainbow', s=20, alpha=0.8)
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_zlabel('Z (m)')
        ax1.set_title('3D Displacement Cloud')
        plt.colorbar(scatter, ax=ax1, label='Displacement (m)', shrink=0.8)
        
        # Stress cloud
        ax2 = fig.add_subplot(222, projection='3d')
        if len(self.von_mises_stress) > 0:
            # Map stress to points approximately
            stress_points = np.zeros(len(coordinates))
            for i in range(min(len(stress_points), len(self.von_mises_stress))):
                stress_points[i] = self.von_mises_stress[i % len(self.von_mises_stress)]
            
            scatter2 = ax2.scatter(coordinates[:, 0], coordinates[:, 1], coordinates[:, 2],
                                 c=stress_points, cmap='jet', s=20, alpha=0.8)
            plt.colorbar(scatter2, ax=ax2, label='von Mises Stress (Pa)', shrink=0.8)
        
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.set_zlabel('Z (m)')
        ax2.set_title('3D Stress Cloud')
        
        # Top view
        ax3 = fig.add_subplot(223)
        scatter3 = ax3.scatter(coordinates[:, 0], coordinates[:, 1],
                             c=displacement_magnitude, cmap='rainbow', s=15, alpha=0.8)
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Y (m)')
        ax3.set_title('Top View - Displacement')
        ax3.axis('equal')
        plt.colorbar(scatter3, ax=ax3, label='Displacement (m)')
        
        # Summary
        ax4 = fig.add_subplot(224)
        ax4.axis('off')
        summary_text = f"""
Professional FEM Analysis Summary:

Geometry:
• Diameter: {self.radius*2*1000:.1f} mm
• Thickness: {self.total_thickness*1e6:.1f} μm
• Mesh nodes: {len(coordinates)}

Results:
• Max displacement: {np.max(displacement_magnitude)*1e6:.3f} μm
• Max stress: {np.max(self.von_mises_stress)/1e6:.2f} MPa
• Applied force: {self.force_magnitude} N

Material Properties:
• Equivalent E: {self.E_eq/1e9:.2f} GPa
• Equivalent ν: {self.nu_eq:.3f}
        """
        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes,
                fontsize=11, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.suptitle('Professional 3D FEM Analysis Results', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.savefig('/root/FEM/professional_analysis_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Professional analysis visualization saved: professional_analysis_3d.png")
    
    def export_results(self):
        """Export results in standard formats"""
        # Export to XDMF for ParaView
        with io.XDMFFile(self.domain.comm, "/root/FEM/sensor_displacement.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        with io.XDMFFile(self.domain.comm, "/root/FEM/sensor_stress.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("Results exported to XDMF format for ParaView")
    
    def run_complete_analysis(self):
        """Run complete professional analysis"""
        try:
            print("\n=== Professional 3D FEM Analysis Pipeline ===")
            
            print("\n1. Creating high-quality 3D mesh...")
            self.create_3d_mesh(mesh_resolution=15)  # Coarser mesh for better visibility
            
            print("\n2. Solving FEM equations...")
            self.solve_fem_problem()
            
            print("\n3. Creating professional cloud visualizations...")
            self.create_professional_cloud_visualization()
            
            print("\n4. Exporting results...")
            self.export_results()
            
            print("\n=== Analysis Completed Successfully ===")
            print("\nGenerated Files:")
            if HAS_PYVISTA:
                print("  • displacement_cloud_3d.png - Professional displacement cloud")
                print("  • stress_cloud_3d.png - Professional stress cloud") 
                print("  • deformed_shape_3d.png - Deformed shape analysis")
                print("  • wireframe_mesh_3d.png - Pure wireframe mesh model")
                print("  • displacement_cloud_3d.html - Interactive displacement")
                print("  • stress_cloud_3d.html - Interactive stress")
                print("  • deformed_shape_3d.html - Interactive deformed shape")
            else:
                print("  • professional_analysis_3d.png - Complete analysis overview")
            print("  • sensor_displacement.xdmf - ParaView displacement data")
            print("  • sensor_stress.xdmf - ParaView stress data")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """Main function for realistic sensor analysis"""
    # Create realistic 8mm diameter sensor
    sensor = RealisticSensor3D(
        diameter=8e-3,      # 8mm diameter as requested
        force_magnitude=5.0  # Appropriate force for small sensor
    )
    
    # Run complete analysis with professional visualization
    sensor.run_complete_analysis()

if __name__ == "__main__":
    main()