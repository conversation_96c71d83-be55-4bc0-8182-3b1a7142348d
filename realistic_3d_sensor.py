#!/usr/bin/env python3
"""
Realistic 3D Multi-layer Flexible Sensor with Cloud Visualization
Creates professional 3D contour plots similar to commercial FEM software
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')

# DOLFINx imports
try:
    import dolfinx
    from dolfinx import mesh, fem, io, default_scalar_type, plot
    from dolfinx.fem.petsc import LinearProblem
    import ufl
    from mpi4py import MPI
    from petsc4py import PETSc
    import gmsh
    HAS_DOLFINX = True
except ImportError:
    HAS_DOLFINX = False
    print("DOLFINx not available!")

# Try to install and import pyvista for better visualization
try:
    import pyvista as pv
    HAS_PYVISTA = True
    print("Using PyVista for professional 3D visualization")
    # Configure PyVista for better rendering
    pv.set_plot_theme("document")
    pv.global_theme.background = 'white'
    pv.global_theme.window_size = [1200, 800]
except ImportError:
    HAS_PYVISTA = False
    print("Installing PyVista for better visualization...")
    import subprocess
    import sys
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyvista", "--timeout", "60"])
        import pyvista as pv
        HAS_PYVISTA = True
        pv.set_plot_theme("document")
        pv.global_theme.background = 'white'
    except:
        HAS_PYVISTA = False
        import matplotlib.pyplot as plt

class RealisticSensor3D:
    def __init__(self, diameter=8e-3, force_magnitude=10.0):
        """
        Realistic 3D Multi-layer Flexible Sensor (8mm diameter)
        
        Parameters:
        diameter: Sensor diameter (m) - default 8mm
        force_magnitude: Applied force (N)
        """
        if not HAS_DOLFINX:
            raise ImportError("DOLFINx is required for this analysis")
            
        self.radius = diameter / 2.0  # 4mm radius
        self.force_magnitude = force_magnitude
        
        # Realistic layer thicknesses for 8mm sensor
        self.thickness_pcb = 50e-6      # PI substrate: 50μm (thicker for 8mm sensor)
        self.thickness_pvdf = 40e-6     # PVDF film: 40μm
        self.thickness_electrode = 8e-6  # Electrode: 8μm
        self.thickness_contact = 15e-6   # Contact: 15μm
        self.total_thickness = (self.thickness_pcb + self.thickness_pvdf + 
                               self.thickness_electrode + self.thickness_contact)
        
        # Enhanced material properties for flexible sensor
        self.material_props = {
            'PCB': {'E': 4e6, 'nu': 0.38, 'color': '#1f77b4'},      # Flexible PI substrate ~4 MPa
            'PVDF': {'E': 3e9, 'nu': 0.35, 'color': '#ff7f0e'},     # PVDF ~3 GPa (keeps piezo property)
            'Electrode': {'E': 200e6, 'nu': 0.40, 'color': '#2ca02c'}, # Flexible conductor ~200 MPa
            'Contact': {'E': 100e6, 'nu': 0.42, 'color': '#d62728'}  # Soft contact material ~100 MPa
        }
        
        # Calculate equivalent properties
        self.calculate_equivalent_properties()
        
        # DOLFINx objects
        self.comm = MPI.COMM_WORLD
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        self.von_mises_stress = None
        
        print(f"=== Realistic 3D Sensor Analysis (Professional Grade) ===")
        print(f"Sensor diameter: {self.radius*2*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Applied force: {self.force_magnitude} N")
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties"""
        layers = [
            (self.material_props['PCB'], self.thickness_pcb),
            (self.material_props['PVDF'], self.thickness_pvdf),
            (self.material_props['Electrode'], self.thickness_electrode),
            (self.material_props['Contact'], self.thickness_contact)
        ]
        
        # Volume fractions
        volume_fractions = [t/self.total_thickness for _, t in layers]
        
        # Equivalent properties using rule of mixtures
        self.E_eq = sum(props['E'] * vf for (props, _), vf in zip(layers, volume_fractions))
        self.nu_eq = sum(props['nu'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        print(f"Equivalent Material Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.2f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
    
    def create_3d_mesh(self, mesh_resolution=25):
        """Create high-quality 3D mesh with refined geometry"""
        try:
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("realistic_sensor")
            
            # Finer mesh for better visualization
            mesh_size = self.radius / mesh_resolution
            
            # Create bottom circle
            center_bottom = gmsh.model.geo.addPoint(0, 0, 0, mesh_size)
            
            # Create more points for smoother circle
            n_points = 12  # More points for smoother geometry
            bottom_points = []
            for i in range(n_points):
                angle = 2 * np.pi * i / n_points
                x = self.radius * np.cos(angle)
                y = self.radius * np.sin(angle)
                point = gmsh.model.geo.addPoint(x, y, 0, mesh_size)
                bottom_points.append(point)
            
            # Create arcs for bottom circle
            bottom_arcs = []
            for i in range(n_points):
                next_i = (i + 1) % n_points
                arc = gmsh.model.geo.addCircleArc(bottom_points[i], center_bottom, bottom_points[next_i])
                bottom_arcs.append(arc)
            
            circle_loop_bottom = gmsh.model.geo.addCurveLoop(bottom_arcs)
            circle_surface_bottom = gmsh.model.geo.addPlaneSurface([circle_loop_bottom])
            
            # Create top circle
            center_top = gmsh.model.geo.addPoint(0, 0, self.total_thickness, mesh_size)
            top_points = []
            for i in range(n_points):
                angle = 2 * np.pi * i / n_points
                x = self.radius * np.cos(angle)
                y = self.radius * np.sin(angle)
                point = gmsh.model.geo.addPoint(x, y, self.total_thickness, mesh_size)
                top_points.append(point)
            
            top_arcs = []
            for i in range(n_points):
                next_i = (i + 1) % n_points
                arc = gmsh.model.geo.addCircleArc(top_points[i], center_top, top_points[next_i])
                top_arcs.append(arc)
            
            circle_loop_top = gmsh.model.geo.addCurveLoop(top_arcs)
            circle_surface_top = gmsh.model.geo.addPlaneSurface([circle_loop_top])
            
            # Create side surfaces
            side_surfaces = []
            for i in range(n_points):
                next_i = (i + 1) % n_points
                line_bottom = gmsh.model.geo.addLine(bottom_points[i], top_points[i])
                line_top = gmsh.model.geo.addLine(bottom_points[next_i], top_points[next_i])
                
                side_loop = gmsh.model.geo.addCurveLoop([
                    bottom_arcs[i], line_top, -top_arcs[i], -line_bottom
                ])
                side_surface = gmsh.model.geo.addPlaneSurface([side_loop])
                side_surfaces.append(side_surface)
            
            # Create volume
            all_surfaces = [circle_surface_bottom, circle_surface_top] + side_surfaces
            surface_loop = gmsh.model.geo.addSurfaceLoop(all_surfaces)
            volume = gmsh.model.geo.addVolume([surface_loop])
            
            # Synchronize and add physical groups
            gmsh.model.geo.synchronize()
            
            gmsh.model.addPhysicalGroup(3, [volume], 1)
            gmsh.model.setPhysicalName(3, 1, "sensor_volume")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_bottom], 2)
            gmsh.model.setPhysicalName(2, 2, "bottom_surface")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_top], 3)
            gmsh.model.setPhysicalName(2, 3, "top_surface")
            
            # Generate high-quality mesh
            gmsh.option.setNumber("Mesh.Algorithm", 5)  # Delaunay
            gmsh.option.setNumber("Mesh.Algorithm3D", 1)  # Delaunay
            gmsh.option.setNumber("Mesh.Optimize", 1)
            
            gmsh.model.mesh.generate(3)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, cell_markers, self.facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=3
            )
            
            gmsh.finalize()
            
            # Create vector function space
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (3,)))
            
            print(f"High-Quality 3D Mesh Created:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(3).size_local}")
            
        except Exception as e:
            print(f"Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
    
    def solve_fem_problem(self):
        """Solve the complete FEM problem"""
        # Simple boundary conditions - fix bottom surface completely
        def bottom_surface(x):
            return np.isclose(x[2], 0.0, atol=1e-6)
        
        # Apply clamped BC at bottom
        bottom_facets = mesh.locate_entities_boundary(self.domain, 2, bottom_surface)
        bottom_dofs = fem.locate_dofs_topological(self.V, 2, bottom_facets)
        
        zero_displacement = np.zeros(3, dtype=default_scalar_type)
        bc = fem.dirichletbc(zero_displacement, bottom_dofs, self.V)
        self.bcs = [bc]
        
        print(f"Boundary condition debugging:")
        print(f"  Bottom surface DOFs constrained: {len(bottom_dofs)}")
        print(f"  Total DOFs in system: {self.V.dofmap.index_map.size_global}")
        
        # Apply uniform pressure on top surface
        def top_surface(x):
            return np.isclose(x[2], self.total_thickness, atol=1e-6)
        
        top_facets = mesh.locate_entities_boundary(self.domain, 2, top_surface)
        
        # Create function space for pressure
        Q = fem.functionspace(self.domain, ("Lagrange", 1))
        pressure = fem.Function(Q)
        pressure.x.array[:] = -self.force_magnitude / (np.pi * self.radius**2)  # Uniform pressure
        
        # Define the problem
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        def sigma(u):
            return self.lambda_eq * ufl.nabla_div(u) * ufl.Identity(3) + 2*self.mu_eq*epsilon(u)
        
        # Weak form
        a = ufl.inner(sigma(u), epsilon(v)) * ufl.dx
        
        # Apply pressure as surface load
        ds = ufl.Measure("ds", domain=self.domain, subdomain_data=self.facet_markers)
        L = pressure * ufl.dot(ufl.as_vector([0, 0, 1]), v) * ds(3)  # Top surface has marker 3
        
        print(f"Load debugging info:")
        print(f"  Uniform pressure: {pressure.x.array[0]:.2e} Pa")
        print(f"  Applied over full top surface")
        
        # Solve with more robust solver options
        problem = LinearProblem(a, L, self.bcs, 
                               petsc_options={"ksp_type": "cg", "pc_type": "gamg", "ksp_rtol": 1e-8})
        self.u = problem.solve()
        
        # Calculate stress
        S = fem.functionspace(self.domain, ("DG", 0, (3, 3)))
        stress_expr = sigma(self.u)
        self.stress = fem.Function(S)
        stress_projection = fem.Expression(stress_expr, S.element.interpolation_points())
        self.stress.interpolate(stress_projection)
        
        # Calculate von Mises stress
        stress_array = self.stress.x.array.reshape((-1, 9))
        von_mises_stress = []
        
        for i in range(stress_array.shape[0]):
            s = stress_array[i].reshape((3, 3))
            s11, s22, s33 = s[0,0], s[1,1], s[2,2]
            s12, s13, s23 = s[0,1], s[0,2], s[1,2]
            
            von_mises = np.sqrt(0.5 * ((s11-s22)**2 + (s22-s33)**2 + (s33-s11)**2 + 
                                      6*(s12**2 + s13**2 + s23**2)))
            von_mises_stress.append(von_mises)
        
        self.von_mises_stress = np.array(von_mises_stress)
        
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        max_displacement = np.max(displacement_magnitude)
        max_stress = np.max(self.von_mises_stress)
        
        # More detailed displacement analysis
        max_u_x = np.max(np.abs(u_array[:, 0]))
        max_u_y = np.max(np.abs(u_array[:, 1]))
        max_u_z = np.max(np.abs(u_array[:, 2]))
        
        print(f"FEM Solution Completed:")
        print(f"  Max displacement magnitude: {max_displacement:.6e} m = {max_displacement*1e6:.6f} μm")
        print(f"  Max X-displacement: {max_u_x:.6e} m = {max_u_x*1e6:.6f} μm")
        print(f"  Max Y-displacement: {max_u_y:.6e} m = {max_u_y*1e6:.6f} μm")
        print(f"  Max Z-displacement: {max_u_z:.6e} m = {max_u_z*1e6:.6f} μm")
        print(f"  Max von Mises stress: {max_stress:.6e} Pa = {max_stress/1e6:.6f} MPa")
        print(f"  Non-zero displacements: {np.count_nonzero(displacement_magnitude)} out of {len(displacement_magnitude)}")
    
    def create_professional_cloud_visualization(self):
        """Create professional 3D cloud visualization like commercial FEM software"""
        print("Creating professional 3D cloud visualization...")
        
        if not HAS_PYVISTA:
            print("PyVista not available. Skipping visualization.")
            return
        
        try:
            # Convert mesh to PyVista
            topology, cell_types, geometry = plot.vtk_mesh(self.domain, 3)
            grid = pv.UnstructuredGrid(topology, cell_types, geometry)
            
            # Get displacement and stress data
            u_array = self.u.x.array.reshape((-1, 3))
            displacement_magnitude = np.linalg.norm(u_array, axis=1)
            
            # Add data to grid
            grid.point_data["Displacement_Magnitude"] = displacement_magnitude
            grid.point_data["Displacement_X"] = u_array[:, 0]
            grid.point_data["Displacement_Y"] = u_array[:, 1]
            grid.point_data["Displacement_Z"] = u_array[:, 2]
            
            # Interpolate stress data to points
            stress_points = np.zeros(grid.n_points)
            cell_centers = grid.cell_centers()
            
            for i in range(grid.n_points):
                distances = np.linalg.norm(cell_centers.points - grid.points[i], axis=1)
                nearest_cell = np.argmin(distances)
                if nearest_cell < len(self.von_mises_stress):
                    stress_points[i] = self.von_mises_stress[nearest_cell]
            
            grid.point_data["von_Mises_Stress"] = stress_points
            
            # Create multiple professional visualizations
            self._create_displacement_cloud(grid)
            self._create_stress_cloud(grid)
            self._create_deformed_visualization(grid)
            
        except Exception as e:
            print(f"PyVista visualization failed: {e}")
            print("Skipping visualization due to missing dependencies.")
    
    def _create_displacement_cloud(self, grid):
        """Create displacement cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[1200, 800])
        
        # Add mesh with displacement coloring
        mesh_actor = plotter.add_mesh(
            grid, 
            scalars="Displacement_Magnitude",
            cmap='viridis',  # Professional colormap
            show_edges=False,
            opacity=0.9,
            scalar_bar_args={
                'title': 'Displacement (m)',
                'title_font_size': 14,
                'label_font_size': 12,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.2e',
                'font_family': 'arial'
            }
        )
        
        # Add wireframe overlay for better depth perception
        wireframe = grid.extract_surface()
        plotter.add_mesh(wireframe, style='wireframe', color='black', opacity=0.1, line_width=0.5)
        
        # Professional lighting and camera setup
        plotter.add_title('3D Displacement Field - Cloud Visualization', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        
        # Add lighting for professional appearance
        light = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        plotter.add_light(light)
        
        # Save high-resolution image
        plotter.screenshot('/root/FEM/displacement_cloud_3d.png', transparent_background=False)
        plotter.close()
        
        print("Professional displacement cloud saved: displacement_cloud_3d.png")
    
    def _create_stress_cloud(self, grid):
        """Create stress cloud visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[1200, 800])
        
        # Add mesh with stress coloring using professional colormap
        mesh_actor = plotter.add_mesh(
            grid, 
            scalars="von_Mises_Stress",
            cmap='viridis',  # Professional colormap
            show_edges=False,
            opacity=0.9,
            scalar_bar_args={
                'title': 'von Mises Stress (Pa)',
                'title_font_size': 14,
                'label_font_size': 12,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.2e',
                'font_family': 'arial'
            }
        )
        
        # Add contour lines for better visualization
        contours = grid.contour(scalars="von_Mises_Stress", isosurfaces=10)
        plotter.add_mesh(contours, style='wireframe', color='black', opacity=0.3, line_width=1.0)
        
        # Professional setup
        plotter.add_title('3D Stress Distribution - Cloud Visualization', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        
        # Enhanced lighting
        light1 = pv.Light(position=(1, 1, 1), focal_point=(0, 0, 0), color='white')
        light2 = pv.Light(position=(-1, -1, 1), focal_point=(0, 0, 0), color='white', intensity=0.5)
        plotter.add_light(light1)
        plotter.add_light(light2)
        
        plotter.screenshot('/root/FEM/stress_cloud_3d.png', transparent_background=False)
        plotter.close()
        
        print("Professional stress cloud saved: stress_cloud_3d.png")
    
    def _create_deformed_visualization(self, grid):
        """Create deformed shape visualization"""
        plotter = pv.Plotter(off_screen=True, window_size=[1200, 800])
        
        # Scale displacement for visualization
        scale_factor = 1000  # Amplify for visibility
        u_array = self.u.x.array.reshape((-1, 3))
        
        # Create deformed mesh
        deformed_points = grid.points + scale_factor * u_array
        deformed_grid = grid.copy()
        deformed_grid.points = deformed_points
        
        # Show original mesh
        plotter.add_mesh(grid, color='lightgray', opacity=0.3, label='Original')
        
        # Show deformed mesh with displacement coloring
        plotter.add_mesh(
            deformed_grid,
            scalars="Displacement_Magnitude",
            cmap='viridis',
            show_edges=True,
            edge_color='black',
            line_width=0.5,
            opacity=0.9,
            label='Deformed',
            scalar_bar_args={
                'title': f'Displacement (m) - Scale: {scale_factor}x',
                'title_font_size': 14,
                'label_font_size': 12,
                'n_labels': 8,
                'italic': False,
                'fmt': '%.2e',
                'font_family': 'arial'
            }
        )
        
        plotter.add_title('Deformed Shape Analysis (Amplified)', font_size=16, color='black')
        plotter.show_axes()
        plotter.view_isometric()
        plotter.add_legend()
        
        plotter.screenshot('/root/FEM/deformed_shape_3d.png', transparent_background=False)
        plotter.close()
        
        print("Deformed shape visualization saved: deformed_shape_3d.png")
    
    def _create_matplotlib_fallback(self):
        """Fallback visualization using matplotlib"""
        print("Using matplotlib fallback visualization...")
        import matplotlib.pyplot as plt
        
        coordinates = self.domain.geometry.x
        u_array = self.u.x.array.reshape((-1, 3))
        displacement_magnitude = np.linalg.norm(u_array, axis=1)
        
        # Create professional-looking plots
        fig = plt.figure(figsize=(16, 12))
        
        # Displacement cloud
        ax1 = fig.add_subplot(221, projection='3d')
        scatter = ax1.scatter(coordinates[:, 0], coordinates[:, 1], coordinates[:, 2],
                            c=displacement_magnitude, cmap='rainbow', s=20, alpha=0.8)
        ax1.set_xlabel('X (m)')
        ax1.set_ylabel('Y (m)')
        ax1.set_zlabel('Z (m)')
        ax1.set_title('3D Displacement Cloud')
        plt.colorbar(scatter, ax=ax1, label='Displacement (m)', shrink=0.8)
        
        # Stress cloud
        ax2 = fig.add_subplot(222, projection='3d')
        if len(self.von_mises_stress) > 0:
            # Map stress to points approximately
            stress_points = np.zeros(len(coordinates))
            for i in range(min(len(stress_points), len(self.von_mises_stress))):
                stress_points[i] = self.von_mises_stress[i % len(self.von_mises_stress)]
            
            scatter2 = ax2.scatter(coordinates[:, 0], coordinates[:, 1], coordinates[:, 2],
                                 c=stress_points, cmap='jet', s=20, alpha=0.8)
            plt.colorbar(scatter2, ax=ax2, label='von Mises Stress (Pa)', shrink=0.8)
        
        ax2.set_xlabel('X (m)')
        ax2.set_ylabel('Y (m)')
        ax2.set_zlabel('Z (m)')
        ax2.set_title('3D Stress Cloud')
        
        # Top view
        ax3 = fig.add_subplot(223)
        scatter3 = ax3.scatter(coordinates[:, 0], coordinates[:, 1],
                             c=displacement_magnitude, cmap='rainbow', s=15, alpha=0.8)
        ax3.set_xlabel('X (m)')
        ax3.set_ylabel('Y (m)')
        ax3.set_title('Top View - Displacement')
        ax3.axis('equal')
        plt.colorbar(scatter3, ax=ax3, label='Displacement (m)')
        
        # Summary
        ax4 = fig.add_subplot(224)
        ax4.axis('off')
        summary_text = f"""
Professional FEM Analysis Summary:

Geometry:
• Diameter: {self.radius*2*1000:.1f} mm
• Thickness: {self.total_thickness*1e6:.1f} μm
• Mesh nodes: {len(coordinates)}

Results:
• Max displacement: {np.max(displacement_magnitude)*1e6:.3f} μm
• Max stress: {np.max(self.von_mises_stress)/1e6:.2f} MPa
• Applied force: {self.force_magnitude} N

Material Properties:
• Equivalent E: {self.E_eq/1e9:.2f} GPa
• Equivalent ν: {self.nu_eq:.3f}
        """
        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes,
                fontsize=11, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.suptitle('Professional 3D FEM Analysis Results', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.savefig('/root/FEM/professional_analysis_3d.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Professional analysis visualization saved: professional_analysis_3d.png")
    
    def export_results(self):
        """Export results in standard formats"""
        # Export to XDMF for ParaView
        with io.XDMFFile(self.domain.comm, "/root/FEM/sensor_displacement.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        with io.XDMFFile(self.domain.comm, "/root/FEM/sensor_stress.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("Results exported to XDMF format for ParaView")
    
    def run_complete_analysis(self):
        """Run complete professional analysis"""
        try:
            print("\n=== Professional 3D FEM Analysis Pipeline ===")
            
            print("\n1. Creating high-quality 3D mesh...")
            self.create_3d_mesh(mesh_resolution=30)  # Higher resolution
            
            print("\n2. Solving FEM equations...")
            self.solve_fem_problem()
            
            print("\n3. Creating professional cloud visualizations...")
            self.create_professional_cloud_visualization()
            
            print("\n4. Exporting results...")
            self.export_results()
            
            print("\n=== Analysis Completed Successfully ===")
            print("\nGenerated Files:")
            if HAS_PYVISTA:
                print("  • displacement_cloud_3d.png - Professional displacement cloud")
                print("  • stress_cloud_3d.png - Professional stress cloud") 
                print("  • deformed_shape_3d.png - Deformed shape analysis")
                print("  • displacement_cloud_3d.html - Interactive displacement")
                print("  • stress_cloud_3d.html - Interactive stress")
                print("  • deformed_shape_3d.html - Interactive deformed shape")
            else:
                print("  • professional_analysis_3d.png - Complete analysis overview")
            print("  • sensor_displacement.xdmf - ParaView displacement data")
            print("  • sensor_stress.xdmf - ParaView stress data")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """Main function for realistic sensor analysis"""
    # Create realistic 8mm diameter sensor
    sensor = RealisticSensor3D(
        diameter=8e-3,      # 8mm diameter as requested
        force_magnitude=5.0  # Appropriate force for small sensor
    )
    
    # Run complete analysis with professional visualization
    sensor.run_complete_analysis()

if __name__ == "__main__":
    main()