#!/usr/bin/env python3
"""
3D Multi-layer Flexible Pressure Sensor FEM Analysis using FEniCSx

Layer Structure (bottom to top):
1. Flexible PCB substrate (Polyimide PI) - 25μm
2. PVDF piezoelectric film - 35μm  
3. Electrode layer (conductive silver/metal) - 5μm
4. Magnetic contact layer (nickel alloy) - 10μm

Analysis Goals:
- Apply central load and analyze displacement/stress distribution
- Perform strength verification for each layer
- Generate 3D mesh visualization and stress distribution
- Create animated GIF with rotating view
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
# 3D plotting will be handled differently due to matplotlib version conflicts
from mpi4py import MPI
import dolfinx
from dolfinx import mesh, fem, io, default_scalar_type
from dolfinx.fem.petsc import LinearProblem
import ufl
from petsc4py import PETSc
import gmsh
import warnings
warnings.filterwarnings('ignore')

class FlexibleSensor3D:
    """3D Multi-layer Flexible Pressure Sensor FEM Analysis"""
    
    def __init__(self, radius=0.025, force_magnitude=10.0, mesh_resolution=20):
        """
        Initialize analysis parameters
        
        Args:
            radius: Circular plate radius (m)
            force_magnitude: Central applied load (N)
            mesh_resolution: Mesh density
        """
        self.radius = radius
        self.force_magnitude = force_magnitude
        self.mesh_resolution = mesh_resolution
        
        # MPI communication
        self.comm = MPI.COMM_WORLD
        
        # Layer thicknesses (m)
        self.thickness_pcb = 25e-6        # PI substrate: 25μm
        self.thickness_pvdf = 35e-6       # PVDF film: 35μm
        self.thickness_electrode = 5e-6   # Electrode layer: 5μm
        self.thickness_contact = 10e-6    # Magnetic contact: 10μm
        self.total_thickness = (self.thickness_pcb + self.thickness_pvdf + 
                               self.thickness_electrode + self.thickness_contact)
        
        # Define material properties
        self.define_material_properties()
        
        # Mesh and function spaces
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        
        print(f"=== 3D Multi-layer Flexible Sensor Analysis ===")
        print(f"Sensor radius: {self.radius*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Central load: {self.force_magnitude} N")
        
    def define_material_properties(self):
        """Define material properties for each layer"""
        
        # PI substrate properties
        self.pcb_props = {
            'E': 2.8e9,              # Young's modulus (Pa)
            'nu': 0.35,              # Poisson's ratio
            'density': 1420,         # Density (kg/m³)
            'yield_strength': 85e6,  # Yield strength (Pa)
            'name': 'PI Substrate'
        }
        
        # PVDF film properties
        self.pvdf_props = {
            'E': 2.0e9,              # Young's modulus (Pa)
            'nu': 0.39,              # Poisson's ratio
            'density': 1780,         # Density (kg/m³)
            'yield_strength': 50e6,  # Yield strength (Pa)
            'name': 'PVDF Film'
        }
        
        # Electrode layer properties (Silver)
        self.electrode_props = {
            'E': 83e9,               # Young's modulus (Pa)
            'nu': 0.37,              # Poisson's ratio
            'density': 10490,        # Density (kg/m³)
            'yield_strength': 170e6, # Yield strength (Pa)
            'name': 'Silver Electrode'
        }
        
        # Magnetic contact layer properties (Nickel alloy)
        self.contact_props = {
            'E': 200e9,              # Young's modulus (Pa)
            'nu': 0.31,              # Poisson's ratio
            'density': 8900,         # Density (kg/m³)
            'yield_strength': 300e6, # Yield strength (Pa)
            'name': 'Nickel Contact'
        }
        
        # Calculate equivalent properties using rule of mixtures
        self.calculate_equivalent_properties()
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties"""
        
        layers = [
            (self.pcb_props, self.thickness_pcb),
            (self.pvdf_props, self.thickness_pvdf),
            (self.electrode_props, self.thickness_electrode),
            (self.contact_props, self.thickness_contact)
        ]
        
        # Volume fractions
        volume_fractions = [t/self.total_thickness for _, t in layers]
        
        # Equivalent Young's modulus (Voigt model)
        self.E_eq = sum(props['E'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        # Equivalent Poisson's ratio
        self.nu_eq = sum(props['nu'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        print(f"Equivalent Material Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.2f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
        print(f"  Lamé parameter λ: {self.lambda_eq/1e9:.2f} GPa")
        print(f"  Shear modulus μ: {self.mu_eq/1e9:.2f} GPa")
        
    def create_3d_mesh(self):
        """Create 3D layered mesh using Gmsh"""
        try:
            # Initialize Gmsh
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("layered_sensor")
            
            mesh_size = self.radius / self.mesh_resolution
            
            # Create bottom circle (z=0)
            center_bottom = gmsh.model.geo.addPoint(0, 0, 0, mesh_size)
            p1_bottom = gmsh.model.geo.addPoint(self.radius, 0, 0, mesh_size)
            p2_bottom = gmsh.model.geo.addPoint(0, self.radius, 0, mesh_size)
            p3_bottom = gmsh.model.geo.addPoint(-self.radius, 0, 0, mesh_size)
            p4_bottom = gmsh.model.geo.addPoint(0, -self.radius, 0, mesh_size)
            
            # Create arcs for bottom circle
            arc1_bottom = gmsh.model.geo.addCircleArc(p1_bottom, center_bottom, p2_bottom)
            arc2_bottom = gmsh.model.geo.addCircleArc(p2_bottom, center_bottom, p3_bottom)
            arc3_bottom = gmsh.model.geo.addCircleArc(p3_bottom, center_bottom, p4_bottom)
            arc4_bottom = gmsh.model.geo.addCircleArc(p4_bottom, center_bottom, p1_bottom)
            
            # Create circle loop and surface for bottom
            circle_loop_bottom = gmsh.model.geo.addCurveLoop([arc1_bottom, arc2_bottom, arc3_bottom, arc4_bottom])
            circle_surface_bottom = gmsh.model.geo.addPlaneSurface([circle_loop_bottom])
            
            # Create top circle (z=total_thickness)
            center_top = gmsh.model.geo.addPoint(0, 0, self.total_thickness, mesh_size)
            p1_top = gmsh.model.geo.addPoint(self.radius, 0, self.total_thickness, mesh_size)
            p2_top = gmsh.model.geo.addPoint(0, self.radius, self.total_thickness, mesh_size)
            p3_top = gmsh.model.geo.addPoint(-self.radius, 0, self.total_thickness, mesh_size)
            p4_top = gmsh.model.geo.addPoint(0, -self.radius, self.total_thickness, mesh_size)
            
            # Create arcs for top circle
            arc1_top = gmsh.model.geo.addCircleArc(p1_top, center_top, p2_top)
            arc2_top = gmsh.model.geo.addCircleArc(p2_top, center_top, p3_top)
            arc3_top = gmsh.model.geo.addCircleArc(p3_top, center_top, p4_top)
            arc4_top = gmsh.model.geo.addCircleArc(p4_top, center_top, p1_top)
            
            # Create circle loop and surface for top
            circle_loop_top = gmsh.model.geo.addCurveLoop([arc1_top, arc2_top, arc3_top, arc4_top])
            circle_surface_top = gmsh.model.geo.addPlaneSurface([circle_loop_top])
            
            # Create side surfaces
            line1 = gmsh.model.geo.addLine(p1_bottom, p1_top)
            line2 = gmsh.model.geo.addLine(p2_bottom, p2_top)
            line3 = gmsh.model.geo.addLine(p3_bottom, p3_top)
            line4 = gmsh.model.geo.addLine(p4_bottom, p4_top)
            
            # Create side surface loops
            side_loop1 = gmsh.model.geo.addCurveLoop([arc1_bottom, line2, -arc1_top, -line1])
            side_loop2 = gmsh.model.geo.addCurveLoop([arc2_bottom, line3, -arc2_top, -line2])
            side_loop3 = gmsh.model.geo.addCurveLoop([arc3_bottom, line4, -arc3_top, -line3])
            side_loop4 = gmsh.model.geo.addCurveLoop([arc4_bottom, line1, -arc4_top, -line4])
            
            side_surface1 = gmsh.model.geo.addPlaneSurface([side_loop1])
            side_surface2 = gmsh.model.geo.addPlaneSurface([side_loop2])
            side_surface3 = gmsh.model.geo.addPlaneSurface([side_loop3])
            side_surface4 = gmsh.model.geo.addPlaneSurface([side_loop4])
            
            # Create surface loop and volume
            surface_loop = gmsh.model.geo.addSurfaceLoop([
                circle_surface_bottom, circle_surface_top,
                side_surface1, side_surface2, side_surface3, side_surface4
            ])
            volume = gmsh.model.geo.addVolume([surface_loop])
            
            # Synchronize geometry
            gmsh.model.geo.synchronize()
            
            # Add physical groups
            gmsh.model.addPhysicalGroup(3, [volume], 1)
            gmsh.model.setPhysicalName(3, 1, "sensor_volume")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_bottom], 2)
            gmsh.model.setPhysicalName(2, 2, "bottom_surface")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_top], 3)
            gmsh.model.setPhysicalName(2, 3, "top_surface")
            
            gmsh.model.addPhysicalGroup(2, [side_surface1, side_surface2, side_surface3, side_surface4], 4)
            gmsh.model.setPhysicalName(2, 4, "side_surfaces")
            
            # Generate 3D mesh
            gmsh.model.mesh.generate(3)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, cell_markers, facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=3
            )
            
            # Store facet markers for boundary conditions
            self.facet_markers = facet_markers
            
            # Clean up Gmsh
            gmsh.finalize()
            
            # Create vector function space (3D displacement field)
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (3,)))
            
            print(f"3D Mesh Created Successfully:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(3).size_local}")
            
        except Exception as e:
            print(f"3D Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
            
    def define_boundary_conditions(self):
        """Define boundary conditions for 3D model"""
        # Fixed bottom surface (z=0)
        def bottom_surface(x):
            return np.isclose(x[2], 0.0, atol=1e-6)
        
        # Find DOFs on bottom surface
        bottom_facets = mesh.locate_entities_boundary(self.domain, 2, bottom_surface)
        bottom_dofs = fem.locate_dofs_topological(self.V, 2, bottom_facets)
        
        # Create zero displacement boundary condition
        zero_displacement = np.zeros(3, dtype=default_scalar_type)
        bc = fem.dirichletbc(zero_displacement, bottom_dofs, self.V)
        
        self.bcs = [bc]
        
        print(f"Boundary Conditions:")
        print(f"  Fixed bottom surface facets: {len(bottom_facets)}")
        print(f"  Fixed DOFs: {len(bottom_dofs)}")
        
    def define_load(self):
        """Define applied load on top center"""
        # Create source term for point load at center of top surface
        x = ufl.SpatialCoordinate(self.domain)
        
        # Point load at center of top surface
        load_radius = self.radius * 0.1  # Load applied over small central area
        
        # Gaussian distribution for load
        load_intensity = self.force_magnitude / (np.pi * load_radius**2)
        f_expr = ufl.conditional(
            ufl.And(
                ufl.le((x[0]**2 + x[1]**2), load_radius**2),
                ufl.ge(x[2], self.total_thickness * 0.9)
            ),
            ufl.as_vector([0, 0, -load_intensity]),
            ufl.as_vector([0, 0, 0])
        )
        
        self.f = f_expr
        
        print(f"Load Definition:")
        print(f"  Load magnitude: {self.force_magnitude} N")
        print(f"  Load radius: {load_radius*1000:.2f} mm")
        print(f"  Load intensity: {load_intensity:.2e} Pa")
        
    def solve_elasticity(self):
        """Solve 3D elasticity problem"""
        print("Solving 3D elasticity equations...")
        
        # Define trial and test functions
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        # Define stress tensor
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        def sigma(u):
            return self.lambda_eq * ufl.nabla_div(u) * ufl.Identity(3) + 2*self.mu_eq*epsilon(u)
        
        # Variational problem
        a = ufl.inner(sigma(u), epsilon(v)) * ufl.dx
        L = ufl.dot(self.f, v) * ufl.dx
        
        # Solve linear problem
        problem = LinearProblem(a, L, self.bcs, 
                               petsc_options={"ksp_type": "preonly", "pc_type": "lu"})
        self.u = problem.solve()
        
        print("Solution completed!")
        
        # Calculate maximum displacement
        u_array = self.u.x.array.reshape((-1, 3))
        max_displacement = np.max(np.linalg.norm(u_array, axis=1))
        print(f"Maximum displacement: {max_displacement*1e6:.3f} μm")
        
    def calculate_stress(self):
        """Calculate stress field"""
        print("Calculating stress field...")
        
        # Create tensor function space for stress
        S = fem.functionspace(self.domain, ("DG", 0, (3, 3)))
        
        # Define stress expression
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        def sigma(u):
            return self.lambda_eq * ufl.nabla_div(u) * ufl.Identity(3) + 2*self.mu_eq*epsilon(u)
        
        # Project stress onto function space
        stress_expr = sigma(self.u)
        self.stress = fem.Function(S)
        stress_projection = fem.Expression(stress_expr, S.element.interpolation_points())
        self.stress.interpolate(stress_projection)
        
        # Calculate von Mises stress
        stress_array = self.stress.x.array.reshape((-1, 9))
        von_mises_stress = []
        
        for i in range(stress_array.shape[0]):
            s = stress_array[i].reshape((3, 3))
            # von Mises stress calculation
            s11, s22, s33 = s[0,0], s[1,1], s[2,2]
            s12, s13, s23 = s[0,1], s[0,2], s[1,2]
            
            von_mises = np.sqrt(0.5 * ((s11-s22)**2 + (s22-s33)**2 + (s33-s11)**2 + 
                                      6*(s12**2 + s13**2 + s23**2)))
            von_mises_stress.append(von_mises)
        
        self.von_mises_stress = np.array(von_mises_stress)
        max_stress = np.max(self.von_mises_stress)
        print(f"Maximum von Mises stress: {max_stress/1e6:.2f} MPa")
        
    def perform_strength_check(self):
        """Perform strength verification for each layer"""
        print(f"\n=== Strength Verification Analysis ===\n")
        
        max_stress = np.max(self.von_mises_stress)
        
        layers = [
            (self.pcb_props, "PI Substrate"),
            (self.pvdf_props, "PVDF Film"),
            (self.electrode_props, "Silver Electrode"),
            (self.contact_props, "Nickel Contact")
        ]
        
        min_safety_factor = float('inf')
        
        for props, name in layers:
            yield_strength = props['yield_strength']
            safety_factor = yield_strength / max_stress if max_stress > 0 else float('inf')
            min_safety_factor = min(min_safety_factor, safety_factor)
            
            status = "✓ Safe" if safety_factor >= 2.0 else "✗ Unsafe"
            
            print(f"{name}:")
            print(f"  Yield strength: {yield_strength/1e6:.1f} MPa")
            print(f"  Maximum stress: {max_stress/1e6:.2f} MPa")
            print(f"  Safety factor: {safety_factor:.2f}")
            print(f"  {status} (target: 2.0)\n")
        
        print(f"Overall Assessment:")
        print(f"  Minimum safety factor: {min_safety_factor:.2f}")
        status = "✓ Overall structure safe" if min_safety_factor >= 2.0 else "✗ Overall structure unsafe"
        print(f"  {status}")
        
    def export_results(self):
        """Export results to XDMF format"""
        if self.domain is None:
            return
            
        # Export displacement
        with io.XDMFFile(self.domain.comm, "displacement_3d.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        # Export stress
        with io.XDMFFile(self.domain.comm, "stress_3d.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("Results exported to displacement_3d.xdmf and stress_3d.xdmf")
        
    def create_3d_visualization(self):
        """Create 3D visualization and animated GIF"""
        if self.domain is None:
            return
            
        print("Creating 3D visualization...")
        
        # Get mesh coordinates and displacement
        coordinates = self.domain.geometry.x
        u_array = self.u.x.array.reshape((-1, 3))
        
        # Get mesh connectivity
        cells = self.domain.topology.connectivity(3, 0)
        
        # Create figure for animation
        fig = plt.figure(figsize=(12, 8))
        
        def update_plot(frame):
            fig.clear()
            
            # Create 2D projections since 3D plotting has conflicts
            ax1 = fig.add_subplot(221)
            ax2 = fig.add_subplot(222)
            ax3 = fig.add_subplot(223)
            ax4 = fig.add_subplot(224)
            
            x, y, z = coordinates[:, 0], coordinates[:, 1], coordinates[:, 2]
            
            # XY projection (top view)
            ax1.scatter(x, y, c=z, cmap='viridis', s=2, alpha=0.7)
            ax1.set_xlabel('X (m)')
            ax1.set_ylabel('Y (m)')
            ax1.set_title('Top View (XY)')
            ax1.axis('equal')
            
            # XZ projection (side view)
            ax2.scatter(x, z, c=y, cmap='plasma', s=2, alpha=0.7)
            ax2.set_xlabel('X (m)')
            ax2.set_ylabel('Z (m)')
            ax2.set_title('Side View (XZ)')
            
            # YZ projection (front view)
            ax3.scatter(y, z, c=x, cmap='coolwarm', s=2, alpha=0.7)
            ax3.set_xlabel('Y (m)')
            ax3.set_ylabel('Z (m)')
            ax3.set_title('Front View (YZ)')
            
            # Displacement magnitude
            displacement_mag = np.linalg.norm(u_array, axis=1)
            scatter = ax4.scatter(x, y, c=displacement_mag, cmap='hot', s=2, alpha=0.8)
            ax4.set_xlabel('X (m)')
            ax4.set_ylabel('Y (m)')
            ax4.set_title('Displacement Magnitude')
            ax4.axis('equal')
            
            # Add colorbar for displacement
            plt.colorbar(scatter, ax=ax4, label='Displacement (m)')
            
            plt.suptitle(f'3D Sensor Analysis - Frame {frame}')
            plt.tight_layout()
        
        # Create animation
        frames = 180  # Full rotation
        ani = animation.FuncAnimation(fig, update_plot, frames=frames, interval=50)
        
        # Save as GIF
        print("Saving 3D animation as GIF...")
        ani.save('sensor_3d_analysis.gif', writer='pillow', fps=20)
        print("3D animation saved as sensor_3d_analysis.gif")
        
        # Create static plot
        plt.figure(figsize=(15, 10))
        
        # Mesh plot
        plt.subplot(2, 3, 1)
        x, y = coordinates[:, 0], coordinates[:, 1]
        plt.triplot(x, y, alpha=0.5)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.title('2D Mesh Projection')
        plt.axis('equal')
        
        # Displacement magnitude
        plt.subplot(2, 3, 2)
        displacement_mag = np.linalg.norm(u_array, axis=1)
        scatter = plt.tricontourf(x, y, displacement_mag, levels=50, cmap='viridis')
        plt.colorbar(scatter, label='Displacement (m)')
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.title('Displacement Magnitude')
        plt.axis('equal')
        
        # Stress distribution
        plt.subplot(2, 3, 3)
        # Get stress at mesh points (simplified)
        stress_scatter = plt.tricontourf(x, y, self.von_mises_stress[:len(x)], 
                                       levels=50, cmap='plasma')
        plt.colorbar(stress_scatter, label='von Mises Stress (Pa)')
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.title('von Mises Stress Distribution')
        plt.axis('equal')
        
        # Layer structure diagram
        plt.subplot(2, 3, 4)
        layers = ['Ni Contact', 'Ag Electrode', 'PVDF Film', 'PI Substrate']
        thicknesses = [self.thickness_contact, self.thickness_electrode, 
                      self.thickness_pvdf, self.thickness_pcb]
        colors = ['#C0C0C0', '#FFD700', '#FF6B6B', '#4ECDC4']
        
        bottom = 0
        for i, (layer, thickness, color) in enumerate(zip(layers, thicknesses, colors)):
            plt.barh(i, thickness*1e6, left=bottom, color=color, alpha=0.7)
            plt.text(bottom + thickness*1e6/2, i, f'{layer}\n{thickness*1e6:.0f}μm', 
                    ha='center', va='center', fontsize=8)
            bottom += thickness*1e6
        
        plt.xlabel('Thickness (μm)')
        plt.title('Layer Structure')
        plt.yticks([])
        
        # Material properties
        plt.subplot(2, 3, 5)
        materials = ['PI', 'PVDF', 'Ag', 'Ni']
        E_values = [self.pcb_props['E']/1e9, self.pvdf_props['E']/1e9,
                   self.electrode_props['E']/1e9, self.contact_props['E']/1e9]
        
        bars = plt.bar(materials, E_values, color=colors)
        plt.ylabel('Young\'s Modulus (GPa)')
        plt.title('Material Properties')
        plt.xticks(rotation=45)
        
        # Add values on bars
        for bar, value in zip(bars, E_values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{value:.1f}', ha='center', va='bottom')
        
        # Analysis summary
        plt.subplot(2, 3, 6)
        plt.axis('off')
        summary_text = f"""
Analysis Summary:
• Sensor Radius: {self.radius*1000:.1f} mm
• Total Thickness: {self.total_thickness*1e6:.1f} μm
• Applied Load: {self.force_magnitude:.1f} N
• Max Displacement: {np.max(np.linalg.norm(u_array, axis=1))*1e6:.3f} μm
• Max Stress: {np.max(self.von_mises_stress)/1e6:.2f} MPa
• Nodes: {self.domain.geometry.x.shape[0]}
• Elements: {self.domain.topology.index_map(3).size_local}
"""
        plt.text(0.1, 0.9, summary_text, transform=plt.gca().transAxes, 
                fontsize=10, verticalalignment='top', fontfamily='monospace')
        
        plt.suptitle('3D Multi-layer Flexible Sensor Analysis Results', fontsize=16)
        plt.tight_layout()
        plt.savefig('sensor_3d_results.png', dpi=300, bbox_inches='tight')
        print("Static analysis plot saved as sensor_3d_results.png")
        
    def run_analysis(self):
        """Run complete 3D analysis"""
        try:
            self.create_3d_mesh()
            self.define_boundary_conditions()
            self.define_load()
            self.solve_elasticity()
            self.calculate_stress()
            self.perform_strength_check()
            self.export_results()
            self.create_3d_visualization()
            
            print(f"\n3D Analysis completed successfully!")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """Main function"""
    # Create and run 3D analysis
    sensor = FlexibleSensor3D(radius=0.025, force_magnitude=10.0, mesh_resolution=15)
    sensor.run_analysis()

if __name__ == "__main__":
    main()