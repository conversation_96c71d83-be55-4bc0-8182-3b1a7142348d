import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as tri
from matplotlib.patches import Circle, Polygon
import warnings
warnings.filterwarnings('ignore')

class CircularPlateFEM:
    def __init__(self, radius=1.0, thickness=0.01, E=210e9, nu=0.3):
        """
        圆板有限元分析类
        
        参数:
        radius: 圆板半径 (m)
        thickness: 板厚 (m)
        E: 弹性模量 (Pa)
        nu: 泊松比
        """
        self.radius = radius
        self.thickness = thickness
        self.E = E
        self.nu = nu
        
        # 计算弯曲刚度
        self.D = E * thickness**3 / (12 * (1 - nu**2))
        
        # 网格和节点信息
        self.nodes = None
        self.elements = None
        self.num_nodes = 0
        self.num_elements = 0
        
        # 边界条件和载荷
        self.fixed_nodes = []
        
        # 结果
        self.displacement = None
        self.stress = None
        
    def generate_mesh(self, num_radial=15, num_angular=30):
        """
        生成圆板网格
        """
        nodes_list = []
        
        # 中心点
        nodes_list.append([0.0, 0.0])
        
        # 径向和周向网格点
        for i in range(1, num_radial):
            r = i * self.radius / (num_radial - 1)
            n_theta = max(8, int(num_angular * r / self.radius))
            for j in range(n_theta):
                theta = 2 * np.pi * j / n_theta
                x = r * np.cos(theta)
                y = r * np.sin(theta)
                nodes_list.append([x, y])
        
        self.nodes = np.array(nodes_list)
        self.num_nodes = len(self.nodes)
        
        # 使用Delaunay三角剖分生成单元
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1])
        self.elements = triangulation.triangles
        
        # 过滤掉超出圆板范围的单元
        valid_elements = []
        for elem in self.elements:
            center_x = np.mean(self.nodes[elem, 0])
            center_y = np.mean(self.nodes[elem, 1])
            if center_x**2 + center_y**2 <= self.radius**2:
                valid_elements.append(elem)
        
        self.elements = np.array(valid_elements)
        self.num_elements = len(self.elements)
        
        print(f"网格生成完成: {self.num_nodes} 个节点, {self.num_elements} 个单元")
        
    def apply_boundary_conditions(self, force_magnitude=1000):
        """
        施加边界条件：四周固定，中心受集中载荷
        """
        # 找到边界节点
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        self.fixed_nodes = np.where(distances >= 0.95 * self.radius)[0]
        
        # 中心节点
        center_node = np.argmin(distances)
        
        print(f"边界条件设置完成: {len(self.fixed_nodes)} 个边界节点被固定")
        print(f"中心载荷: {force_magnitude} N 施加在节点 {center_node}")
        
        return center_node, force_magnitude
        
    def solve_system(self):
        """
        求解系统 - 使用解析解近似
        """
        print("使用解析解近似求解...")
        
        # 获取中心节点和载荷
        center_node, force = self.apply_boundary_conditions()
        
        # 简化的解析解计算位移
        self.displacement = np.zeros(self.num_nodes)
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        
        # 圆板中心集中载荷的解析解
        P = force  # 载荷
        a = self.radius  # 半径
        
        for i, r in enumerate(distances):
            if i in self.fixed_nodes:
                self.displacement[i] = 0
            else:
                # 简化的位移公式
                if r < 0.01:  # 中心附近
                    self.displacement[i] = -P * a**2 / (16 * np.pi * self.D) * (1 + self.nu)
                else:
                    self.displacement[i] = -P / (16 * np.pi * self.D) * (
                        (3 + self.nu) * a**2 * np.log(a/r) + 
                        (1 - self.nu) * r**2
                    )
        
        # 计算应力
        self.compute_stress()
        
        print("求解完成!")
        
    def compute_stress(self):
        """
        计算应力
        """
        self.stress = np.zeros(self.num_nodes)
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        
        # 基于位移计算应力
        for i, r in enumerate(distances):
            if r < 0.01:  # 中心附近
                self.stress[i] = abs(self.displacement[i]) * self.E / (2 * self.radius)
            else:
                self.stress[i] = abs(self.displacement[i]) * self.E / (4 * r)
    
    def plot_mesh(self):
        """
        绘制网格图
        """
        plt.figure(figsize=(10, 8))
        
        # 绘制单元
        for elem in self.elements:
            elem_nodes = self.nodes[elem]
            triangle = Polygon(elem_nodes, fill=False, edgecolor='black', linewidth=0.5)
            plt.gca().add_patch(triangle)
        
        # 绘制节点
        plt.scatter(self.nodes[:, 0], self.nodes[:, 1], c='red', s=8)
        
        # 高亮边界节点
        if len(self.fixed_nodes) > 0:
            plt.scatter(self.nodes[self.fixed_nodes, 0], self.nodes[self.fixed_nodes, 1], 
                       c='blue', s=12, label='固定边界')
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='blue', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.grid(True, alpha=0.3)
        plt.title('圆板有限元网格', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.legend()
        plt.tight_layout()
        plt.savefig('/root/FEM/mesh.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_displacement(self):
        """
        绘制位移云图
        """
        plt.figure(figsize=(10, 8))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        # 绘制位移云图
        contour = plt.tricontourf(triangulation, self.displacement, levels=20, cmap='viridis')
        plt.colorbar(contour, label='位移 (m)')
        
        # 添加等高线
        plt.tricontour(triangulation, self.displacement, levels=10, colors='black', alpha=0.4, linewidths=0.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.title('圆板位移云图', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.tight_layout()
        plt.savefig('/root/FEM/displacement.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 输出结果
        max_displacement = np.max(np.abs(self.displacement))
        center_displacement = abs(self.displacement[0])
        
        print(f"最大位移: {max_displacement:.6f} m")
        print(f"中心位移: {center_displacement:.6f} m")
        
    def plot_stress(self):
        """
        绘制应力云图
        """
        plt.figure(figsize=(10, 8))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        # 绘制应力云图
        contour = plt.tricontourf(triangulation, self.stress, levels=20, cmap='plasma')
        plt.colorbar(contour, label='等效应力 (Pa)')
        
        # 添加等高线
        plt.tricontour(triangulation, self.stress, levels=10, colors='black', alpha=0.4, linewidths=0.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.title('圆板应力云图', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.tight_layout()
        plt.savefig('/root/FEM/stress.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 输出结果
        max_stress = np.max(self.stress)
        max_stress_idx = np.argmax(self.stress)
        
        print(f"最大应力: {max_stress:.2f} Pa")
        print(f"最大应力位置: ({self.nodes[max_stress_idx, 0]:.3f}, {self.nodes[max_stress_idx, 1]:.3f})")

def main():
    """
    主函数：运行圆板有限元分析
    """
    # 创建圆板有限元分析实例 - 可自定义材料参数
    plate = CircularPlateFEM(
        radius=0.5,          # 圆板半径 (m)
        thickness=0.005,     # 板厚 (m)  
        E=200e9,             # 弹性模量 (Pa) - 钢材
        nu=0.3               # 泊松比
    )
    
    print("=== 圆板有限元分析 ===")
    print(f"圆板半径: {plate.radius} m")
    print(f"板厚: {plate.thickness} m") 
    print(f"弹性模量: {plate.E:.0e} Pa")
    print(f"泊松比: {plate.nu}")
    
    # 生成网格
    print("\n1. 生成网格...")
    plate.generate_mesh(num_radial=15, num_angular=30)
    
    # 求解
    print("\n2. 求解有限元方程...")
    plate.solve_system()
    
    # 绘制结果
    print("\n3. 生成结果图...")
    
    # 网格图
    print("绘制网格图...")
    plate.plot_mesh()
    
    # 位移云图  
    print("绘制位移云图...")
    plate.plot_displacement()
    
    # 应力云图
    print("绘制应力云图...")
    plate.plot_stress()
    
    print("\n分析完成! 图片已保存到 /root/FEM/ 目录")

if __name__ == "__main__":
    main()