import numpy as np
import warnings
warnings.filterwarnings('ignore')

# DOLFINx imports
import dolfinx
from dolfinx import mesh, fem, io, default_scalar_type, plot
from dolfinx.fem.petsc import LinearProblem
import ufl
from mpi4py import MPI
from petsc4py import PETSc
import gmsh

# Visualization imports
try:
    import pyvista as pv
    HAS_PYVISTA = True
    print("Using PyVista for 3D visualization")
except ImportError:
    HAS_PYVISTA = False
    print("PyVista not available, using dolfinx.plot for visualization")
    import matplotlib.pyplot as plt

class FlexibleSensor3DFEM:
    def __init__(self, radius=0.025, thickness_layers=None, E_layers=None, nu_layers=None, force_magnitude=10.0):
        """
        3D Multi-layer Flexible Sensor FEM Analysis using DOLFINx
        
        Parameters:
        radius: Sensor radius (m)
        thickness_layers: List of layer thicknesses [PCB, PVDF, Electrode, Contact] (m)
        E_layers: List of <PERSON>'s moduli for each layer (Pa)
        nu_layers: List of Poisson's ratios for each layer
        force_magnitude: Applied force (N)
        """
        self.radius = radius
        self.force_magnitude = force_magnitude
        
        # Default layer properties if not specified
        if thickness_layers is None:
            self.thickness_layers = [25e-6, 35e-6, 5e-6, 10e-6]  # PCB, PVDF, Electrode, Contact
        else:
            self.thickness_layers = thickness_layers
            
        if E_layers is None:
            self.E_layers = [2.8e9, 2.0e9, 83e9, 200e9]  # PI, PVDF, Silver, Nickel
        else:
            self.E_layers = E_layers
            
        if nu_layers is None:
            self.nu_layers = [0.35, 0.39, 0.37, 0.31]  # PI, PVDF, Silver, Nickel
        else:
            self.nu_layers = nu_layers
        
        self.total_thickness = sum(self.thickness_layers)
        
        # Calculate equivalent properties
        self.calculate_equivalent_properties()
        
        # MPI communicator
        self.comm = MPI.COMM_WORLD
        
        # DOLFINx objects
        self.domain = None
        self.V = None
        self.u = None
        self.stress = None
        self.facet_markers = None
        
        print(f"=== 3D Multi-layer Flexible Sensor FEM Analysis ===")
        print(f"Sensor radius: {self.radius*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Applied force: {self.force_magnitude} N")
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties using rule of mixtures"""
        # Volume fractions
        volume_fractions = [t/self.total_thickness for t in self.thickness_layers]
        
        # Equivalent Young's modulus (Voigt model)
        self.E_eq = sum(E * vf for E, vf in zip(self.E_layers, volume_fractions))
        
        # Equivalent Poisson's ratio
        self.nu_eq = sum(nu * vf for nu, vf in zip(self.nu_layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        print(f"Equivalent Material Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.2f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
    
    def create_3d_mesh(self, mesh_resolution=20):
        """Create 3D cylindrical mesh using Gmsh"""
        try:
            # Initialize Gmsh
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("sensor_3d")
            
            mesh_size = self.radius / mesh_resolution
            
            # Create bottom circle (z=0)
            center_bottom = gmsh.model.geo.addPoint(0, 0, 0, mesh_size)
            p1_bottom = gmsh.model.geo.addPoint(self.radius, 0, 0, mesh_size)
            p2_bottom = gmsh.model.geo.addPoint(0, self.radius, 0, mesh_size)
            p3_bottom = gmsh.model.geo.addPoint(-self.radius, 0, 0, mesh_size)
            p4_bottom = gmsh.model.geo.addPoint(0, -self.radius, 0, mesh_size)
            
            # Create arcs for bottom circle
            arc1_bottom = gmsh.model.geo.addCircleArc(p1_bottom, center_bottom, p2_bottom)
            arc2_bottom = gmsh.model.geo.addCircleArc(p2_bottom, center_bottom, p3_bottom)
            arc3_bottom = gmsh.model.geo.addCircleArc(p3_bottom, center_bottom, p4_bottom)
            arc4_bottom = gmsh.model.geo.addCircleArc(p4_bottom, center_bottom, p1_bottom)
            
            circle_loop_bottom = gmsh.model.geo.addCurveLoop([arc1_bottom, arc2_bottom, arc3_bottom, arc4_bottom])
            circle_surface_bottom = gmsh.model.geo.addPlaneSurface([circle_loop_bottom])
            
            # Create top circle (z=total_thickness)
            center_top = gmsh.model.geo.addPoint(0, 0, self.total_thickness, mesh_size)
            p1_top = gmsh.model.geo.addPoint(self.radius, 0, self.total_thickness, mesh_size)
            p2_top = gmsh.model.geo.addPoint(0, self.radius, self.total_thickness, mesh_size)
            p3_top = gmsh.model.geo.addPoint(-self.radius, 0, self.total_thickness, mesh_size)
            p4_top = gmsh.model.geo.addPoint(0, -self.radius, self.total_thickness, mesh_size)
            
            arc1_top = gmsh.model.geo.addCircleArc(p1_top, center_top, p2_top)
            arc2_top = gmsh.model.geo.addCircleArc(p2_top, center_top, p3_top)
            arc3_top = gmsh.model.geo.addCircleArc(p3_top, center_top, p4_top)
            arc4_top = gmsh.model.geo.addCircleArc(p4_top, center_top, p1_top)
            
            circle_loop_top = gmsh.model.geo.addCurveLoop([arc1_top, arc2_top, arc3_top, arc4_top])
            circle_surface_top = gmsh.model.geo.addPlaneSurface([circle_loop_top])
            
            # Create side surfaces
            line1 = gmsh.model.geo.addLine(p1_bottom, p1_top)
            line2 = gmsh.model.geo.addLine(p2_bottom, p2_top)
            line3 = gmsh.model.geo.addLine(p3_bottom, p3_top)
            line4 = gmsh.model.geo.addLine(p4_bottom, p4_top)
            
            side_loop1 = gmsh.model.geo.addCurveLoop([arc1_bottom, line2, -arc1_top, -line1])
            side_loop2 = gmsh.model.geo.addCurveLoop([arc2_bottom, line3, -arc2_top, -line2])
            side_loop3 = gmsh.model.geo.addCurveLoop([arc3_bottom, line4, -arc3_top, -line3])
            side_loop4 = gmsh.model.geo.addCurveLoop([arc4_bottom, line1, -arc4_top, -line4])
            
            side_surface1 = gmsh.model.geo.addPlaneSurface([side_loop1])
            side_surface2 = gmsh.model.geo.addPlaneSurface([side_loop2])
            side_surface3 = gmsh.model.geo.addPlaneSurface([side_loop3])
            side_surface4 = gmsh.model.geo.addPlaneSurface([side_loop4])
            
            # Create volume
            surface_loop = gmsh.model.geo.addSurfaceLoop([
                circle_surface_bottom, circle_surface_top,
                side_surface1, side_surface2, side_surface3, side_surface4
            ])
            volume = gmsh.model.geo.addVolume([surface_loop])
            
            # Synchronize and add physical groups
            gmsh.model.geo.synchronize()
            
            gmsh.model.addPhysicalGroup(3, [volume], 1)
            gmsh.model.setPhysicalName(3, 1, "sensor_volume")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_bottom], 2)
            gmsh.model.setPhysicalName(2, 2, "bottom_surface")
            
            gmsh.model.addPhysicalGroup(2, [circle_surface_top], 3)
            gmsh.model.setPhysicalName(2, 3, "top_surface")
            
            # Generate 3D mesh
            gmsh.model.mesh.generate(3)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, cell_markers, self.facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=3
            )
            
            gmsh.finalize()
            
            # Create vector function space (3D displacement field)
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (3,)))
            
            print(f"3D Mesh Created:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(3).size_local}")
            
        except Exception as e:
            print(f"3D Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise
        
    def apply_boundary_conditions(self, force_magnitude=1000):
        """
        施加边界条件：四周固定，中心受集中载荷
        """
        # 找到边界节点
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        self.fixed_nodes = np.where(distances >= 0.95 * self.radius)[0]
        
        # 中心节点
        center_node = np.argmin(distances)
        
        print(f"边界条件设置完成: {len(self.fixed_nodes)} 个边界节点被固定")
        print(f"中心载荷: {force_magnitude} N 施加在节点 {center_node}")
        
        return center_node, force_magnitude
        
    def solve_system(self):
        """
        求解系统 - 使用解析解近似
        """
        print("使用解析解近似求解...")
        
        # 获取中心节点和载荷
        center_node, force = self.apply_boundary_conditions()
        
        # 简化的解析解计算位移
        self.displacement = np.zeros(self.num_nodes)
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        
        # 圆板中心集中载荷的解析解
        P = force  # 载荷
        a = self.radius  # 半径
        
        for i, r in enumerate(distances):
            if i in self.fixed_nodes:
                self.displacement[i] = 0
            else:
                # 简化的位移公式
                if r < 0.01:  # 中心附近
                    self.displacement[i] = -P * a**2 / (16 * np.pi * self.D) * (1 + self.nu)
                else:
                    self.displacement[i] = -P / (16 * np.pi * self.D) * (
                        (3 + self.nu) * a**2 * np.log(a/r) + 
                        (1 - self.nu) * r**2
                    )
        
        # 计算应力
        self.compute_stress()
        
        print("求解完成!")
        
    def compute_stress(self):
        """
        计算应力
        """
        self.stress = np.zeros(self.num_nodes)
        distances = np.sqrt(self.nodes[:, 0]**2 + self.nodes[:, 1]**2)
        
        # 基于位移计算应力
        for i, r in enumerate(distances):
            if r < 0.01:  # 中心附近
                self.stress[i] = abs(self.displacement[i]) * self.E / (2 * self.radius)
            else:
                self.stress[i] = abs(self.displacement[i]) * self.E / (4 * r)
    
    def plot_mesh(self):
        """
        绘制网格图
        """
        plt.figure(figsize=(10, 8))
        
        # 绘制单元
        for elem in self.elements:
            elem_nodes = self.nodes[elem]
            triangle = Polygon(elem_nodes, fill=False, edgecolor='black', linewidth=0.5)
            plt.gca().add_patch(triangle)
        
        # 绘制节点
        plt.scatter(self.nodes[:, 0], self.nodes[:, 1], c='red', s=8)
        
        # 高亮边界节点
        if len(self.fixed_nodes) > 0:
            plt.scatter(self.nodes[self.fixed_nodes, 0], self.nodes[self.fixed_nodes, 1], 
                       c='blue', s=12, label='固定边界')
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='blue', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.grid(True, alpha=0.3)
        plt.title('圆板有限元网格', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.legend()
        plt.tight_layout()
        plt.savefig('/root/FEM/mesh.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_displacement(self):
        """
        绘制位移云图
        """
        plt.figure(figsize=(10, 8))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        # 绘制位移云图
        contour = plt.tricontourf(triangulation, self.displacement, levels=20, cmap='viridis')
        plt.colorbar(contour, label='位移 (m)')
        
        # 添加等高线
        plt.tricontour(triangulation, self.displacement, levels=10, colors='black', alpha=0.4, linewidths=0.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.title('圆板位移云图', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.tight_layout()
        plt.savefig('/root/FEM/displacement.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 输出结果
        max_displacement = np.max(np.abs(self.displacement))
        center_displacement = abs(self.displacement[0])
        
        print(f"最大位移: {max_displacement:.6f} m")
        print(f"中心位移: {center_displacement:.6f} m")
        
    def plot_stress(self):
        """
        绘制应力云图
        """
        plt.figure(figsize=(10, 8))
        
        # 创建三角网格用于插值
        triangulation = tri.Triangulation(self.nodes[:, 0], self.nodes[:, 1], self.elements)
        
        # 绘制应力云图
        contour = plt.tricontourf(triangulation, self.stress, levels=20, cmap='plasma')
        plt.colorbar(contour, label='等效应力 (Pa)')
        
        # 添加等高线
        plt.tricontour(triangulation, self.stress, levels=10, colors='black', alpha=0.4, linewidths=0.5)
        
        # 添加圆形边界
        circle = Circle((0, 0), self.radius, fill=False, edgecolor='white', linewidth=2)
        plt.gca().add_patch(circle)
        
        plt.axis('equal')
        plt.title('圆板应力云图', fontsize=14)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.tight_layout()
        plt.savefig('/root/FEM/stress.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 输出结果
        max_stress = np.max(self.stress)
        max_stress_idx = np.argmax(self.stress)
        
        print(f"最大应力: {max_stress:.2f} Pa")
        print(f"最大应力位置: ({self.nodes[max_stress_idx, 0]:.3f}, {self.nodes[max_stress_idx, 1]:.3f})")

    def export_results(self):
        """Export results to XDMF format"""
        if self.domain is None:
            return
            
        # Export displacement
        with io.XDMFFile(self.domain.comm, "/root/FEM/displacement_3d.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.u)
        
        # Export stress
        with io.XDMFFile(self.domain.comm, "/root/FEM/stress_3d.xdmf", "w") as xdmf:
            xdmf.write_mesh(self.domain)
            xdmf.write_function(self.stress)
        
        print("Results exported to XDMF format")
    
    def run_analysis(self):
        """Run complete 3D FEM analysis"""
        try:
            print("\n1. Creating 3D mesh...")
            self.create_3d_mesh()
            
            print("\n2. Defining boundary conditions...")
            self.define_boundary_conditions()
            
            print("\n3. Defining load...")
            self.define_load()
            
            print("\n4. Solving elasticity equations...")
            self.solve_elasticity()
            
            print("\n5. Calculating stress field...")
            self.calculate_stress()
            
            print("\n6. Creating visualizations...")
            self.visualize_3d_mesh()
            self.visualize_3d_displacement()
            self.visualize_3d_stress()
            
            print("\n7. Exporting results...")
            self.export_results()
            
            print("\n=== Analysis completed successfully! ===")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """
    Main function: Run 3D multi-layer flexible sensor FEM analysis
    """
    # Create 3D sensor analysis instance
    sensor = FlexibleSensor3DFEM(
        radius=0.025,                                    # Sensor radius (m)
        thickness_layers=[25e-6, 35e-6, 5e-6, 10e-6],  # Layer thicknesses (m)
        E_layers=[2.8e9, 2.0e9, 83e9, 200e9],          # Young's moduli (Pa)
        nu_layers=[0.35, 0.39, 0.37, 0.31],            # Poisson's ratios
        force_magnitude=10.0                             # Applied force (N)
    )
    
    # Run complete analysis
    sensor.run_analysis()
    
    print("\nAnalysis complete! Results saved in /root/FEM/")
    if HAS_PYVISTA:
        print("Interactive 3D visualizations:")
        print("  - mesh_3d.html (3D mesh)")
        print("  - displacement_3d.html (3D displacement)")
        print("  - stress_3d.html (3D stress)")
    else:
        print("Static visualizations:")
        print("  - mesh_3d.png")
        print("  - displacement_3d.png")
        print("  - stress_3d.png")

if __name__ == "__main__":
    main()