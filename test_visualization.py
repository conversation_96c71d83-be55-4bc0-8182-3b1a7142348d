#!/usr/bin/env python3
"""
测试可视化效果的简单脚本
"""

import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

def check_image_quality(image_path):
    """检查图像质量和内容"""
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return False
    
    try:
        img = Image.open(image_path)
        width, height = img.size
        
        # 转换为numpy数组进行分析
        img_array = np.array(img)
        
        # 检查是否是纯色图像（可能表示渲染问题）
        if len(img_array.shape) == 3:
            # RGB图像
            unique_colors = len(np.unique(img_array.reshape(-1, img_array.shape[2]), axis=0))
        else:
            # 灰度图像
            unique_colors = len(np.unique(img_array))
        
        print(f"✅ {os.path.basename(image_path)}:")
        print(f"   尺寸: {width}x{height}")
        print(f"   颜色数量: {unique_colors}")
        
        # 如果颜色数量太少，可能有问题
        if unique_colors < 10:
            print(f"   ⚠️  警告: 颜色数量较少，可能存在渲染问题")
            return False
        else:
            print(f"   ✅ 图像质量正常")
            return True
            
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return False

def main():
    """主函数"""
    print("=== 3D传感器可视化质量检查 ===\n")
    
    # 检查所有生成的图像文件
    image_files = [
        'displacement_cloud_3d.png',
        'stress_cloud_3d.png', 
        'safety_factor_cloud_3d.png',
        'deformed_shape_3d.png',
        'wireframe_mesh_3d.png'
    ]
    
    results = {}
    for image_file in image_files:
        results[image_file] = check_image_quality(image_file)
        print()
    
    # 总结
    print("=== 检查结果总结 ===")
    good_images = sum(results.values())
    total_images = len(results)
    
    print(f"正常图像: {good_images}/{total_images}")
    
    if good_images == total_images:
        print("🎉 所有可视化图像质量正常！")
    else:
        print("⚠️  部分图像可能存在问题，建议检查:")
        for img, status in results.items():
            if not status:
                print(f"   - {img}")
    
    # 检查数据文件
    print("\n=== 数据文件检查 ===")
    data_files = [
        'sensor_displacement.xdmf',
        'sensor_stress.xdmf',
        'sensor_displacement.h5',
        'sensor_stress.h5'
    ]
    
    for data_file in data_files:
        if os.path.exists(data_file):
            size = os.path.getsize(data_file)
            print(f"✅ {data_file}: {size} bytes")
        else:
            print(f"❌ {data_file}: 文件不存在")

if __name__ == "__main__":
    main()
