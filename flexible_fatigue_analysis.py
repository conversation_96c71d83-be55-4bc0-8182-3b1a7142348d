#!/usr/bin/env python3
"""
Flexible Sensor Bending Fatigue Analysis with Animation
Advanced fatigue life prediction for multi-layer flexible sensors under cyclic bending
"""

import numpy as np
import warnings
warnings.filterwarnings('ignore')

# DOLFINx imports
try:
    import dolfinx
    from dolfinx import mesh, fem, io, default_scalar_type, plot
    from dolfinx.fem.petsc import LinearProblem
    import ufl
    from mpi4py import MPI
    from petsc4py import PETSc
    import gmsh
    HAS_DOLFINX = True
except ImportError:
    HAS_DOLFINX = False
    print("DOLFINx not available!")

# Visualization imports
try:
    import pyvista as pv
    import matplotlib.pyplot as plt
    import matplotlib.animation as animation
    from matplotlib.patches import Circle
    HAS_PYVISTA = True
    pv.set_plot_theme("document")
    pv.global_theme.background = 'white'
except ImportError:
    HAS_PYVISTA = False
    import matplotlib.pyplot as plt
    import matplotlib.animation as animation
    from matplotlib.patches import Circle

import os
import time

class FlexibleSensorFatigueAnalysis:
    def __init__(self, length=20e-3, width=8e-3, bending_radius=5e-3, cycles=10000):
        """
        Flexible Sensor Bending Fatigue Analysis
        
        Parameters:
        length: Sensor length (m) - default 20mm
        width: Sensor width (m) - default 8mm  
        bending_radius: Minimum bending radius (m) - default 5mm
        cycles: Number of fatigue cycles to analyze
        """
        if not HAS_DOLFINX:
            raise ImportError("DOLFINx is required for this analysis")
            
        self.length = length
        self.width = width
        self.bending_radius = bending_radius
        self.cycles = cycles
        
        # Same material properties as the original sensor
        self.thickness_pcb = 50e-6      # PI substrate: 50μm
        self.thickness_pvdf = 40e-6     # PVDF film: 40μm
        self.thickness_electrode = 8e-6  # Electrode: 8μm
        self.thickness_contact = 15e-6   # Contact: 15μm
        self.total_thickness = (self.thickness_pcb + self.thickness_pvdf + 
                               self.thickness_electrode + self.thickness_contact)
        
        # Enhanced material properties with fatigue data
        self.material_props = {
            'PCB': {
                'E': 4e6, 'nu': 0.38, 'color': '#1f77b4',      # Flexible PI substrate ~4 MPa
                'fatigue_strength': 2e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.1,  # Fatigue exponent (b)
                'ultimate_strength': 8e6,  # Ultimate tensile strength (Pa)
                'endurance_limit': 1e6     # Endurance limit (Pa)
            },
            'PVDF': {
                'E': 3e9, 'nu': 0.35, 'color': '#ff7f0e',     # PVDF ~3 GPa
                'fatigue_strength': 50e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.08, # Fatigue exponent (b)
                'ultimate_strength': 80e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 20e6    # Endurance limit (Pa)
            },
            'Electrode': {
                'E': 200e6, 'nu': 0.40, 'color': '#2ca02c', # Flexible conductor ~200 MPa
                'fatigue_strength': 30e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.12, # Fatigue exponent (b)
                'ultimate_strength': 60e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 10e6    # Endurance limit (Pa)
            },
            'Contact': {
                'E': 100e6, 'nu': 0.42, 'color': '#d62728',  # Soft contact material ~100 MPa
                'fatigue_strength': 15e6,  # Fatigue strength at 10^6 cycles (Pa)
                'fatigue_exponent': -0.15, # Fatigue exponent (b)
                'ultimate_strength': 30e6, # Ultimate tensile strength (Pa)
                'endurance_limit': 5e6     # Endurance limit (Pa)
            }
        }
        
        # Calculate equivalent properties
        self.calculate_equivalent_properties()
        
        # FEM objects
        self.comm = MPI.COMM_WORLD
        self.domain = None
        self.V = None
        self.bending_angles = np.linspace(0, 2*np.pi, 20)  # Full bending cycle
        self.stress_history = []
        self.fatigue_damage = []
        
        print(f"=== Flexible Sensor Fatigue Analysis ===")
        print(f"Sensor dimensions: {self.length*1000:.1f} x {self.width*1000:.1f} mm")
        print(f"Total thickness: {self.total_thickness*1e6:.1f} μm")
        print(f"Bending radius: {self.bending_radius*1000:.1f} mm")
        print(f"Fatigue cycles: {self.cycles}")
        
    def calculate_equivalent_properties(self):
        """Calculate equivalent material properties for multi-layer structure"""
        layers = [
            (self.material_props['PCB'], self.thickness_pcb),
            (self.material_props['PVDF'], self.thickness_pvdf),
            (self.material_props['Electrode'], self.thickness_electrode),
            (self.material_props['Contact'], self.thickness_contact)
        ]
        
        # Volume fractions
        volume_fractions = [t/self.total_thickness for _, t in layers]
        
        # Equivalent properties using rule of mixtures
        self.E_eq = sum(props['E'] * vf for (props, _), vf in zip(layers, volume_fractions))
        self.nu_eq = sum(props['nu'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        # Lamé parameters
        self.lambda_eq = (self.E_eq * self.nu_eq) / ((1 + self.nu_eq) * (1 - 2*self.nu_eq))
        self.mu_eq = self.E_eq / (2 * (1 + self.nu_eq))
        
        # Equivalent fatigue properties
        self.fatigue_strength_eq = sum(props['fatigue_strength'] * vf for (props, _), vf in zip(layers, volume_fractions))
        self.fatigue_exponent_eq = sum(props['fatigue_exponent'] * vf for (props, _), vf in zip(layers, volume_fractions))
        self.endurance_limit_eq = sum(props['endurance_limit'] * vf for (props, _), vf in zip(layers, volume_fractions))
        
        print(f"Equivalent Properties:")
        print(f"  Young's Modulus: {self.E_eq/1e9:.3f} GPa")
        print(f"  Poisson's Ratio: {self.nu_eq:.3f}")
        print(f"  Fatigue Strength: {self.fatigue_strength_eq/1e6:.2f} MPa")
        print(f"  Endurance Limit: {self.endurance_limit_eq/1e6:.2f} MPa")

    def create_2d_beam_mesh(self, mesh_resolution=40):
        """Create 2D beam mesh for bending analysis"""
        try:
            gmsh.initialize()
            gmsh.clear()
            gmsh.model.add("flexible_beam")
            
            mesh_size = self.length / mesh_resolution
            
            # Create rectangular beam
            # Bottom-left corner
            p1 = gmsh.model.geo.addPoint(0, 0, 0, mesh_size)
            # Bottom-right corner  
            p2 = gmsh.model.geo.addPoint(self.length, 0, 0, mesh_size)
            # Top-right corner
            p3 = gmsh.model.geo.addPoint(self.length, self.total_thickness, 0, mesh_size)
            # Top-left corner
            p4 = gmsh.model.geo.addPoint(0, self.total_thickness, 0, mesh_size)
            
            # Create lines
            l1 = gmsh.model.geo.addLine(p1, p2)  # Bottom
            l2 = gmsh.model.geo.addLine(p2, p3)  # Right
            l3 = gmsh.model.geo.addLine(p3, p4)  # Top
            l4 = gmsh.model.geo.addLine(p4, p1)  # Left
            
            # Create surface
            loop = gmsh.model.geo.addCurveLoop([l1, l2, l3, l4])
            surface = gmsh.model.geo.addPlaneSurface([loop])
            
            # Synchronize
            gmsh.model.geo.synchronize()
            
            # Add physical groups
            gmsh.model.addPhysicalGroup(2, [surface], 1)
            gmsh.model.setPhysicalName(2, 1, "beam_surface")
            
            gmsh.model.addPhysicalGroup(1, [l4], 2)  # Left edge (fixed)
            gmsh.model.setPhysicalName(1, 2, "fixed_edge")
            
            gmsh.model.addPhysicalGroup(1, [l2], 3)  # Right edge (loaded)
            gmsh.model.setPhysicalName(1, 3, "loaded_edge")
            
            # Generate mesh
            gmsh.option.setNumber("Mesh.Algorithm", 6)  # Frontal-Delaunay
            gmsh.option.setNumber("Mesh.Optimize", 1)
            
            gmsh.model.mesh.generate(2)
            
            # Convert to DOLFINx mesh
            from dolfinx.io import gmshio
            self.domain, cell_markers, self.facet_markers = gmshio.model_to_mesh(
                gmsh.model, self.comm, 0, gdim=2
            )
            
            gmsh.finalize()
            
            # Create vector function space for 2D displacement
            self.V = fem.functionspace(self.domain, ("Lagrange", 1, (2,)))
            
            print(f"2D Beam Mesh Created:")
            print(f"  Nodes: {self.domain.geometry.x.shape[0]}")
            print(f"  Elements: {self.domain.topology.index_map(2).size_local}")
            
        except Exception as e:
            print(f"Mesh creation failed: {e}")
            try:
                gmsh.finalize()
            except:
                pass
            raise

    def solve_bending_step(self, curvature):
        """Solve single bending step with given curvature"""
        # Boundary conditions: fix left edge
        def left_edge(x):
            return np.isclose(x[0], 0.0, atol=1e-6)
        
        left_facets = mesh.locate_entities_boundary(self.domain, 1, left_edge)
        left_dofs = fem.locate_dofs_topological(self.V, 1, left_facets)
        
        zero_displacement = np.zeros(2, dtype=default_scalar_type)
        bc_left = fem.dirichletbc(zero_displacement, left_dofs, self.V)
        
        # Apply moment at right edge based on curvature
        # M = E*I*curvature, where I = w*t³/12 for rectangular section
        I = self.width * self.total_thickness**3 / 12
        moment = self.E_eq * I * curvature
        
        # Convert moment to equivalent force distribution
        force_per_unit_height = 6 * moment / (self.width * self.total_thickness**2)
        
        # Define the problem
        u = ufl.TrialFunction(self.V)
        v = ufl.TestFunction(self.V)
        
        def epsilon(u):
            return 0.5*(ufl.nabla_grad(u) + ufl.nabla_grad(u).T)
        
        def sigma(u):
            return self.lambda_eq * ufl.nabla_div(u) * ufl.Identity(2) + 2*self.mu_eq*epsilon(u)
        
        # Weak form
        a = ufl.inner(sigma(u), epsilon(v)) * ufl.dx
        
        # Apply equivalent body force for bending moment
        # Use a simple uniform force to simulate bending
        force_magnitude = abs(curvature) * self.E_eq * self.total_thickness / self.length
        
        # Create force vector
        f = fem.Constant(self.domain, (0.0, force_magnitude))
        L = ufl.dot(f, v) * ufl.dx
        
        # Solve
        problem = LinearProblem(a, L, [bc_left], 
                               petsc_options={"ksp_type": "cg", "pc_type": "gamg"})
        u_solution = problem.solve()
        
        # Calculate stress
        S = fem.functionspace(self.domain, ("DG", 0, (2, 2)))
        stress_expr = sigma(u_solution)
        stress_function = fem.Function(S)
        stress_projection = fem.Expression(stress_expr, S.element.interpolation_points())
        stress_function.interpolate(stress_projection)
        
        return u_solution, stress_function

    def calculate_fatigue_damage(self, stress_amplitude):
        """Calculate fatigue damage using Palmgren-Miner rule"""
        # S-N curve: N = (Sf/S)^(1/b)
        # where Sf is fatigue strength, S is stress amplitude, b is fatigue exponent
        
        if stress_amplitude <= self.endurance_limit_eq:
            # Infinite life below endurance limit
            cycles_to_failure = np.inf
            damage_per_cycle = 0.0
        else:
            # Calculate cycles to failure using S-N curve
            cycles_to_failure = (self.fatigue_strength_eq / stress_amplitude) ** (1 / abs(self.fatigue_exponent_eq))
            damage_per_cycle = 1.0 / cycles_to_failure
        
        return cycles_to_failure, damage_per_cycle

    def run_fatigue_analysis(self):
        """Run complete fatigue analysis with multiple bending cycles"""
        print("\n=== Running Bending Fatigue Analysis ===")
        
        # Create mesh
        self.create_2d_beam_mesh()
        
        # Calculate maximum curvature from bending radius
        max_curvature = 1.0 / self.bending_radius
        
        print(f"Maximum curvature: {max_curvature:.2e} m⁻¹")
        
        # Storage for analysis results
        cycle_results = []
        cumulative_damage = 0.0
        
        # Analyze one complete bending cycle
        print("\nAnalyzing bending cycle...")
        max_stress_in_cycle = 0.0
        
        for i, angle in enumerate(self.bending_angles):
            # Sinusoidal bending: curvature varies from -max to +max
            curvature = max_curvature * np.sin(angle)
            
            # Solve FEM
            u_solution, stress_function = self.solve_bending_step(curvature)
            
            # Extract stress data
            stress_array = stress_function.x.array.reshape((-1, 4))  # 2x2 stress tensor
            
            # Calculate von Mises stress
            von_mises_stress = []
            for j in range(stress_array.shape[0]):
                s = stress_array[j].reshape((2, 2))
                s11, s22, s12 = s[0,0], s[1,1], s[0,1]
                von_mises = np.sqrt(s11**2 - s11*s22 + s22**2 + 3*s12**2)
                von_mises_stress.append(von_mises)
            
            max_stress = np.max(von_mises_stress)
            max_stress_in_cycle = max(max_stress_in_cycle, max_stress)
            
            cycle_results.append({
                'angle': angle,
                'curvature': curvature,
                'displacement': u_solution,
                'stress': stress_function,
                'max_stress': max_stress,
                'von_mises': von_mises_stress
            })
            
            if i % 5 == 0:
                print(f"  Step {i+1}/{len(self.bending_angles)}: θ={angle*180/np.pi:.1f}°, σ_max={max_stress/1e6:.3f} MPa")
        
        # Calculate fatigue damage for the cycle
        stress_amplitude = max_stress_in_cycle / 2  # Assuming fully reversed loading
        cycles_to_failure, damage_per_cycle = self.calculate_fatigue_damage(stress_amplitude)
        
        total_damage = damage_per_cycle * self.cycles
        
        print(f"\n=== Fatigue Analysis Results ===")
        print(f"Maximum stress amplitude: {stress_amplitude/1e6:.3f} MPa")
        print(f"Cycles to failure: {cycles_to_failure:.0e}")
        print(f"Damage per cycle: {damage_per_cycle:.6e}")
        print(f"Total damage after {self.cycles} cycles: {total_damage:.6f}")
        print(f"Fatigue life factor: {1/total_damage:.2f}")
        
        if total_damage >= 1.0:
            print("⚠️  WARNING: Fatigue failure predicted!")
        else:
            print("✓ Sensor should survive the specified cycles")
        
        self.cycle_results = cycle_results
        self.fatigue_summary = {
            'max_stress_amplitude': stress_amplitude,
            'cycles_to_failure': cycles_to_failure,
            'damage_per_cycle': damage_per_cycle,
            'total_damage': total_damage,
            'life_factor': 1/total_damage if total_damage > 0 else np.inf
        }
        
        return cycle_results

    def create_bending_animation(self):
        """Create animation of bending cycle"""
        print("\n=== Creating Bending Animation ===")
        
        if not hasattr(self, 'cycle_results'):
            print("No cycle results available. Run fatigue analysis first.")
            return
        
        # Set up the figure
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Animation data storage
        frames_data = []
        
        for result in self.cycle_results:
            # Get mesh coordinates (2D)
            coords = self.domain.geometry.x[:, :2]  # Take only x,y coordinates
            u_array = result['displacement'].x.array.reshape((-1, 2))
            
            # Calculate deformed coordinates
            scale_factor = 100  # Amplify deformation for visibility
            deformed_coords = coords + scale_factor * u_array
            
            frames_data.append({
                'coords': coords,
                'deformed_coords': deformed_coords,
                'stress': result['max_stress'],
                'angle': result['angle'],
                'curvature': result['curvature']
            })
        
        def animate(frame):
            # Clear all axes
            ax1.clear()
            ax2.clear()
            ax3.clear()
            ax4.clear()
            
            data = frames_data[frame]
            
            # Plot 1: Original vs Deformed shape
            ax1.scatter(data['coords'][:, 0]*1000, data['coords'][:, 1]*1e6, 
                       c='lightgray', s=10, alpha=0.5, label='Original')
            ax1.scatter(data['deformed_coords'][:, 0]*1000, data['deformed_coords'][:, 1]*1e6,
                       c='red', s=10, alpha=0.8, label='Deformed (100x)')
            ax1.set_xlabel('Length (mm)')
            ax1.set_ylabel('Thickness (μm)')
            ax1.set_title(f'Bending Deformation - θ={data["angle"]*180/np.pi:.1f}°')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # Plot 2: Stress distribution
            colors = plt.cm.viridis(data['stress'] / (max([r['max_stress'] for r in self.cycle_results]) + 1e-10))
            ax2.scatter(data['coords'][:, 0]*1000, data['coords'][:, 1]*1e6, 
                       c=[colors], s=20, alpha=0.8)
            ax2.set_xlabel('Length (mm)')
            ax2.set_ylabel('Thickness (μm)')
            ax2.set_title(f'Stress: {data["stress"]/1e6:.3f} MPa')
            ax2.grid(True, alpha=0.3)
            
            # Plot 3: Curvature vs time
            angles = [r['angle'] for r in self.cycle_results[:frame+1]]
            curvatures = [r['curvature'] for r in self.cycle_results[:frame+1]]
            ax3.plot(np.array(angles)*180/np.pi, np.array(curvatures), 'b-', linewidth=2)
            ax3.axhline(y=1/self.bending_radius, color='r', linestyle='--', label='Max curvature')
            ax3.axhline(y=-1/self.bending_radius, color='r', linestyle='--')
            ax3.plot(data['angle']*180/np.pi, data['curvature'], 'ro', markersize=8)
            ax3.set_xlabel('Bending Angle (°)')
            ax3.set_ylabel('Curvature (m⁻¹)')
            ax3.set_title('Bending Cycle')
            ax3.grid(True, alpha=0.3)
            ax3.legend()
            
            # Plot 4: Stress vs time
            stresses = [r['max_stress']/1e6 for r in self.cycle_results[:frame+1]]
            ax4.plot(np.array(angles)*180/np.pi, stresses, 'g-', linewidth=2)
            ax4.axhline(y=self.fatigue_strength_eq/1e6, color='r', linestyle='--', label='Fatigue strength')
            ax4.axhline(y=self.endurance_limit_eq/1e6, color='orange', linestyle='--', label='Endurance limit')
            ax4.plot(data['angle']*180/np.pi, data['stress']/1e6, 'go', markersize=8)
            ax4.set_xlabel('Bending Angle (°)')
            ax4.set_ylabel('Max Stress (MPa)')
            ax4.set_title('Stress History')
            ax4.grid(True, alpha=0.3)
            ax4.legend()
            
            plt.tight_layout()
        
        # Create animation
        print("Generating animation frames...")
        anim = animation.FuncAnimation(fig, animate, frames=len(frames_data), 
                                     interval=200, repeat=True, blit=False)
        
        # Save animation
        anim.save('/root/FEM/bending_fatigue_animation.gif', writer='pillow', fps=5, dpi=100)
        plt.savefig('/root/FEM/fatigue_analysis_summary.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Animation saved: bending_fatigue_animation.gif")
        print("Summary plot saved: fatigue_analysis_summary.png")

    def generate_fatigue_report(self):
        """Generate comprehensive fatigue analysis report"""
        print("\n=== Generating Fatigue Report ===")
        
        # Create detailed report plot
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # S-N Curve
        stress_range = np.logspace(4, 8, 100)  # 10 kPa to 100 MPa
        cycles_range = []
        for stress in stress_range:
            if stress <= self.endurance_limit_eq:
                cycles_range.append(1e10)  # Very high number for infinite life
            else:
                cycles = (self.fatigue_strength_eq / stress) ** (1 / abs(self.fatigue_exponent_eq))
                cycles_range.append(cycles)
        
        ax1.loglog(cycles_range, stress_range/1e6, 'b-', linewidth=2, label='S-N Curve')
        ax1.axhline(y=self.endurance_limit_eq/1e6, color='r', linestyle='--', label='Endurance Limit')
        ax1.axhline(y=self.fatigue_summary['max_stress_amplitude']/1e6, color='g', linestyle='--', 
                   label=f'Operating Stress: {self.fatigue_summary["max_stress_amplitude"]/1e6:.2f} MPa')
        ax1.axvline(x=self.fatigue_summary['cycles_to_failure'], color='g', linestyle='--', alpha=0.7)
        ax1.set_xlabel('Cycles to Failure')
        ax1.set_ylabel('Stress Amplitude (MPa)')
        ax1.set_title('S-N Curve - Fatigue Life Prediction')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Damage accumulation
        cycle_numbers = np.arange(1, self.cycles + 1)
        cumulative_damage = self.fatigue_summary['damage_per_cycle'] * cycle_numbers
        
        ax2.plot(cycle_numbers, cumulative_damage, 'r-', linewidth=2)
        ax2.axhline(y=1.0, color='k', linestyle='--', label='Failure Criterion')
        ax2.set_xlabel('Cycle Number')
        ax2.set_ylabel('Cumulative Damage')
        ax2.set_title('Damage Accumulation (Palmgren-Miner)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Material properties comparison
        materials = list(self.material_props.keys())
        fatigue_strengths = [self.material_props[mat]['fatigue_strength']/1e6 for mat in materials]
        endurance_limits = [self.material_props[mat]['endurance_limit']/1e6 for mat in materials]
        
        x = np.arange(len(materials))
        width = 0.35
        
        ax3.bar(x - width/2, fatigue_strengths, width, label='Fatigue Strength', alpha=0.8)
        ax3.bar(x + width/2, endurance_limits, width, label='Endurance Limit', alpha=0.8)
        ax3.set_xlabel('Material Layer')
        ax3.set_ylabel('Stress (MPa)')
        ax3.set_title('Material Fatigue Properties')
        ax3.set_xticks(x)
        ax3.set_xticklabels(materials)
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # Summary text
        ax4.axis('off')
        summary_text = f"""
FATIGUE ANALYSIS SUMMARY

Sensor Geometry:
• Length: {self.length*1000:.1f} mm
• Width: {self.width*1000:.1f} mm  
• Thickness: {self.total_thickness*1e6:.1f} μm
• Bending radius: {self.bending_radius*1000:.1f} mm

Material Properties (Equivalent):
• Young's Modulus: {self.E_eq/1e9:.2f} GPa
• Fatigue Strength: {self.fatigue_strength_eq/1e6:.2f} MPa
• Endurance Limit: {self.endurance_limit_eq/1e6:.2f} MPa

Fatigue Results:
• Max stress amplitude: {self.fatigue_summary['max_stress_amplitude']/1e6:.3f} MPa
• Cycles to failure: {self.fatigue_summary['cycles_to_failure']:.2e}
• Damage per cycle: {self.fatigue_summary['damage_per_cycle']:.2e}
• Total damage ({self.cycles} cycles): {self.fatigue_summary['total_damage']:.4f}
• Safety factor: {self.fatigue_summary['life_factor']:.2f}

Recommendation:
{'⚠️  REDESIGN REQUIRED' if self.fatigue_summary['total_damage'] >= 1.0 else '✓ DESIGN ACCEPTABLE'}
        """
        
        ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes,
                fontsize=10, verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
        
        plt.suptitle('Flexible Sensor Fatigue Analysis Report', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('/root/FEM/fatigue_analysis_report.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Fatigue report saved: fatigue_analysis_report.png")

    def run_complete_analysis(self):
        """Run complete fatigue analysis pipeline"""
        try:
            print("\n=== Flexible Sensor Bending Fatigue Analysis Pipeline ===")
            
            # Run fatigue analysis
            self.run_fatigue_analysis()
            
            # Create animation
            self.create_bending_animation()
            
            # Generate report
            self.generate_fatigue_report()
            
            print("\n=== Analysis Completed Successfully ===")
            print("\nGenerated Files:")
            print("  • bending_fatigue_animation.gif - Bending cycle animation")
            print("  • fatigue_analysis_summary.png - Summary plots")
            print("  • fatigue_analysis_report.png - Detailed fatigue report")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            raise

def main():
    """Main function for fatigue analysis"""
    # Create flexible sensor fatigue analysis
    fatigue_analyzer = FlexibleSensorFatigueAnalysis(
        length=20e-3,        # 20mm length
        width=8e-3,          # 8mm width
        bending_radius=5e-3, # 5mm bending radius
        cycles=10000         # 10,000 cycles
    )
    
    # Run complete analysis
    fatigue_analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()